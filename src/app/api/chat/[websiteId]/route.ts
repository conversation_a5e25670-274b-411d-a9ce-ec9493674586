import { ConversationMemory } from "@/lib/ai/memory/conversation-memory";
import {
	chatConfigurationsRepository,
	websitesRepository,
} from "@/lib/db/repositories";
import { handleCorsPreflightRequest } from "@/lib/middleware/cors";
import {
	cleanStreamingContent,
	debugContentCleaning,
} from "@/lib/utils/content-cleaner";
import { mastra } from "@/mastra";
import { RuntimeContext } from "@mastra/core/di";
import type { Message } from "ai";
import { createDataStreamResponse } from "ai";
import type { NextRequest } from "next/server";

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

// Initialize conversation memory
const conversationMemory = new ConversationMemory();

/**
 * Handle OPTIONS requests for CORS preflight
 */
export function OPTIONS(request: NextRequest) {
	return handleCorsPreflightRequest(request);
}

export async function POST(
	req: NextRequest,
	{
		params,
	}: {
		params: Promise<{ websiteId: string }>;
	},
) {
	console.log("[Chat API] Received request to /api/chat/[websiteId]");

	try {
		const body = await req.json();
		console.log("[Chat API] Request body:", body);

		const { messages, visitorId, conversationId } = body;
		const { websiteId } = await params;

		console.log(
			`[Chat API] Processing request for websiteId: ${websiteId}, visitorId: ${
				visitorId || "not provided"
			}`,
		);
		console.log(`[Chat API] Number of messages: ${messages?.length || 0}`);

		if (messages?.length > 0) {
			const lastMessage = messages[messages.length - 1];
			console.log(
				`[Chat API] Last message role: ${
					lastMessage.role
				}, content preview: ${lastMessage.content.substring(0, 50)}...`,
			);
		}

		if (!websiteId) {
			// Create a response with CORS headers directly
			const headers = new Headers({
				"Content-Type": "application/json",
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type",
			});

			return new Response(JSON.stringify({ error: "websiteId is required" }), {
				status: 400,
				headers,
			});
		}

		// Verify the website exists
		const website = await websitesRepository.getById(websiteId);
		if (!website) {
			// Create a response with CORS headers directly
			const headers = new Headers({
				"Content-Type": "application/json",
				"Access-Control-Allow-Origin": "*",
				"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type",
			});

			return new Response(JSON.stringify({ error: "Website not found" }), {
				status: 404,
				headers,
			});
		}

		// Get the chat configuration to check allowed domains
		const chatConfig =
			await chatConfigurationsRepository.getByWebsiteId(websiteId);

		// Check if the domain is allowed
		const origin = req.headers.get("origin");
		console.log(
			`[Chat API] Request origin: ${origin || "none"} for website ${websiteId}`,
		);

		// If there's no origin, allow the request (likely a direct API call)
		if (!origin) {
			console.log("[Chat API] No origin provided, allowing the request");
		}
		// If there's an origin and we have allowed domains configured
		else if (
			chatConfig?.allowedDomains &&
			Array.isArray(chatConfig.allowedDomains) &&
			chatConfig.allowedDomains.length > 0
		) {
			try {
				// Extract domain from origin (e.g., https://example.com -> example.com)
				const originDomain = new URL(origin).hostname;
				console.log(`[Chat API] Checking if domain ${originDomain} is allowed`);
				console.log("[Chat API] Allowed domains:", chatConfig.allowedDomains);

				// Check if the domain or any of its parent domains are in the allowed list
				const isDomainAllowed = chatConfig.allowedDomains.some(
					(allowedDomain) => {
						try {
							if (!allowedDomain) return false;

							// Handle case where allowedDomain might not be a valid URL
							let allowedDomainHost: string;
							try {
								// Try to parse as URL first
								allowedDomainHost = new URL(allowedDomain).hostname;
							} catch (e) {
								// If it's not a valid URL, use it as is (assuming it's just a domain)
								allowedDomainHost = allowedDomain;
							}

							// Check if domain matches exactly or is a subdomain
							const isAllowed =
								originDomain === allowedDomainHost ||
								originDomain.endsWith(`.${allowedDomainHost}`);

							console.log(
								`[Chat API] Checking ${originDomain} against ${allowedDomainHost}: ${
									isAllowed ? "allowed" : "not allowed"
								}`,
							);
							return isAllowed;
						} catch (error) {
							console.error(
								`[Chat API] Error checking domain ${allowedDomain}:`,
								error,
							);
							return false;
						}
					},
				);

				if (!isDomainAllowed) {
					console.log(
						`[Chat API] Domain ${originDomain} not allowed for website ${websiteId}`,
					);
					// Create a response with CORS headers directly
					const headers = new Headers({
						"Content-Type": "application/json",
						"Access-Control-Allow-Origin": origin, // Important: Return the actual origin for proper CORS error handling
						"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
						"Access-Control-Allow-Headers": "Content-Type",
					});

					return new Response(
						JSON.stringify({
							error: "Domain not allowed",
							message: `The domain ${originDomain} is not in the allowed domains list for this chat widget. Please contact the widget owner to add this domain.`,
						}),
						{
							status: 403,
							headers,
						},
					);
				}
				console.log(
					`[Chat API] Domain ${originDomain} is allowed for website ${websiteId}`,
				);
			} catch (error) {
				console.error("[Chat API] Error in domain validation:", error);
				// If there's an error in validation, we'll allow the request to proceed
				// This prevents blocking legitimate requests due to validation errors
			}
		} else {
			// If no allowed domains are configured, allow all domains
			console.log(
				`[Chat API] No domain restrictions for website ${websiteId}, allowing all domains`,
			);
		}

		// Generate a visitor ID if not provided
		const actualVisitorId = visitorId || `anonymous-${Date.now()}`;
		console.log(`[Chat API] Using visitor ID: ${actualVisitorId}`);

		// Log the conversation ID if provided
		if (conversationId) {
			console.log(
				`[Chat API] Client provided conversation ID: ${conversationId}`,
			);
		} else {
			console.log(
				"[Chat API] No conversation ID provided by client, will get or create one",
			);
		}

		// Get or create a conversation
		console.log(
			`[Chat API] Getting or creating conversation for websiteId: ${websiteId}, visitorId: ${actualVisitorId}`,
		);
		let conversation: Awaited<
			ReturnType<typeof conversationMemory.getOrCreateConversation>
		>;
		try {
			// If conversationId is provided, try to get that specific conversation first
			if (conversationId) {
				console.log(
					`[Chat API] Trying to get conversation with ID: ${conversationId}`,
				);

				// Check if the conversation ID is a valid UUID format
				const uuidRegex =
					/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
				if (!uuidRegex.test(conversationId)) {
					console.error(
						`[Chat API] Invalid conversation ID format: ${conversationId}. Creating a new conversation.`,
					);
					// Create a new conversation instead of using the invalid ID
					conversation = await conversationMemory.getOrCreateConversation({
						websiteId,
						visitorId: actualVisitorId,
					});
				} else {
					// Try to get the existing conversation with the valid UUID
					const existingConversation =
						await conversationMemory.getConversation(conversationId);

					if (existingConversation) {
						console.log(
							`[Chat API] Found existing conversation with ID: ${existingConversation.id}`,
						);
						conversation = existingConversation;
					} else {
						console.log(
							`[Chat API] Conversation with ID ${conversationId} not found, creating new one`,
						);
						// Create a new conversation without specifying the ID
						// This will generate a new UUID automatically
						conversation = await conversationMemory.getOrCreateConversation({
							websiteId,
							visitorId: actualVisitorId,
						});
					}
				}
			} else {
				// If no conversationId is provided, get or create based on websiteId and visitorId
				conversation = await conversationMemory.getOrCreateConversation({
					websiteId,
					visitorId: actualVisitorId,
				});
			}

			console.log(
				`[Chat API] Using conversation ID: ${conversation.id} for visitor ${actualVisitorId} and website ${websiteId}`,
			);
		} catch (error) {
			console.error(
				"[Chat API] Error getting or creating conversation:",
				error,
			);
			throw error;
		}

		// Get the chat agent from MastrAI
		const chatAgent = mastra.getAgent("chatAgent");

		// Get the last user message
		const lastUserMessage = messages[messages.length - 1];

		// Store the user message in the database
		if (lastUserMessage.role === "user") {
			console.log(
				`[Chat API] Storing user message in conversation: ${conversation.id}`,
			);
			try {
				const message = await conversationMemory.addMessage({
					conversationId: conversation.id,
					content: lastUserMessage.content,
					role: "user",
				});
				console.log(`[Chat API] Stored user message with ID: ${message.id}`);
			} catch (error) {
				console.error("[Chat API] Error storing user message:", error);
				throw error;
			}
		}

		// Create a runtime context to pass the websiteId and user message to the tool
		const runtimeContext = new RuntimeContext();
		runtimeContext.set("websiteId", websiteId);
		runtimeContext.set("conversationId", conversation.id);

		// Add the user's message to the runtime context for the website context tool
		if (lastUserMessage.role === "user") {
			runtimeContext.set("userMessage", lastUserMessage.content);
		}

		// Start streaming the response
		const response = await chatAgent.stream(messages as Message[], {
			runtimeContext,
		});

		// Use AI SDK's createDataStreamResponse for proper content handling
		const streamResponse = createDataStreamResponse({
			async execute(dataStream) {
				// Send conversation ID as custom data at the start
				console.log(
					`[Chat API] Sending conversation ID in stream: ${conversation.id}`,
				);
				dataStream.writeData({ conversationId: conversation.id });

				// Merge the agent response into the data stream
				response.mergeIntoDataStream(dataStream);

				// Get the full response text for storage
				let fullResponseText = "";
				for await (const textPart of response.textStream) {
					fullResponseText += textPart;
				}

				// Store the assistant's response in the database
				// Clean the content before storing to remove formatting artifacts
				if (fullResponseText) {
					try {
						console.log(
							`[Chat API] Storing assistant response in conversation: ${conversation.id}`,
						);
						console.log(
							`[Chat API] Response content length: ${fullResponseText.length}`,
						);

						// Clean the content before storing in database
						const cleanedContent = cleanStreamingContent(fullResponseText);

						// Debug log the cleaning process
						debugContentCleaning(
							fullResponseText,
							cleanedContent,
							"API Storage",
						);

						const message = await conversationMemory.addMessage({
							conversationId: conversation.id,
							content: cleanedContent, // Store cleaned content
							role: "assistant",
						});
						console.log(
							`[Chat API] Stored assistant response with ID: ${message.id}`,
						);
					} catch (error) {
						console.error(
							"[Chat API] Error storing assistant response:",
							error,
						);
					}
				}
			},
			onError: (error) => {
				console.error("[Chat API] Stream error:", error);
				return `Error: ${
					error instanceof Error ? error.message : String(error)
				}`;
			},
		});

		// Add CORS headers to the stream response
		const headers = new Headers(streamResponse.headers);

		// Set the appropriate CORS headers based on allowed domains
		console.log("[Chat API] Setting CORS headers for response");

		if (
			chatConfig?.allowedDomains &&
			Array.isArray(chatConfig.allowedDomains) &&
			chatConfig.allowedDomains.length > 0 &&
			origin
		) {
			try {
				// Extract domain from origin
				const originDomain = new URL(origin).hostname;
				console.log(
					`[Chat API] Checking if domain ${originDomain} is allowed for CORS`,
				);

				// Check if the domain is allowed using the same logic as above
				const isDomainAllowed = chatConfig.allowedDomains.some(
					(allowedDomain) => {
						try {
							if (!allowedDomain) return false;

							// Handle case where allowedDomain might not be a valid URL
							let allowedDomainHost: string;
							try {
								// Try to parse as URL first
								allowedDomainHost = new URL(allowedDomain).hostname;
							} catch (e) {
								// If it's not a valid URL, use it as is (assuming it's just a domain)
								allowedDomainHost = allowedDomain;
							}

							// Check if domain matches exactly or is a subdomain
							return (
								originDomain === allowedDomainHost ||
								originDomain.endsWith(`.${allowedDomainHost}`)
							);
						} catch (error) {
							console.error(
								"[Chat API] Error checking domain for CORS:",
								error,
							);
							return false;
						}
					},
				);

				// If the domain is allowed, set the specific origin
				if (isDomainAllowed) {
					console.log(
						`[Chat API] Setting CORS header for allowed origin: ${origin}`,
					);
					headers.set("Access-Control-Allow-Origin", origin);
				} else {
					// If not allowed but we've reached this point, the domain check earlier passed
					// or was bypassed due to an error, so we'll allow it
					console.log(
						"[Chat API] Setting CORS header with wildcard as fallback",
					);
					headers.set("Access-Control-Allow-Origin", "*");
				}
			} catch (error) {
				console.error("[Chat API] Error in CORS domain validation:", error);
				// If there's an error, use wildcard as fallback
				headers.set("Access-Control-Allow-Origin", "*");
			}
		} else {
			// If no allowed domains are configured, allow all origins
			console.log(
				"[Chat API] No domain restrictions, setting wildcard CORS header",
			);
			headers.set("Access-Control-Allow-Origin", "*");
		}

		headers.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
		headers.set("Access-Control-Allow-Headers", "Content-Type");

		// Return the response with CORS headers
		return new Response(streamResponse.body, {
			status: streamResponse.status,
			statusText: streamResponse.statusText,
			headers,
		});
	} catch (error) {
		console.error("Error in chat API route:", error);
		// Create a response with CORS headers directly
		const headers = new Headers({
			"Content-Type": "application/json",
			"Access-Control-Allow-Origin": "*",
			"Access-Control-Allow-Methods": "GET, POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type",
		});

		return new Response(
			JSON.stringify({ error: "Failed to process chat request" }),
			{
				status: 500,
				headers,
			},
		);
	}
}
