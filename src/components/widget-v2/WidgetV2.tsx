"use client";

import type React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { createRoot } from "react-dom/client";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { cleanStreamingContent } from "../../lib/utils/content-cleaner";
import { Textarea } from "../ui/textarea";

// Types
interface WidgetConfig {
	websiteId: string;
	primaryColor?: string;
	secondaryColor?: string;
	position?: "bottom-right" | "bottom-left";
	welcomeMessage?: string;
	headerText?: string;
	initiallyOpen?: boolean;
	baseUrl?: string;
	visitorId?: string;
	conversationId?: string;
	onConversationIdChange?: (conversationId: string) => void;
}

interface WidgetProps {
	config: WidgetConfig;
	shadowRoot: ShadowRoot | HTMLElement;
}

interface Message {
	id: string;
	content: string;
	role: "user" | "assistant";
	timestamp: Date;
}

// Styles as JavaScript objects for complete isolation
const styles = {
	container: {
		position: "fixed" as const,
		bottom: "20px",
		right: "20px",
		zIndex: 2147483647,
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		fontSize: "14px",
		lineHeight: "1.5",
		color: "#333",
		pointerEvents: "auto" as const,
	},

	containerLeft: {
		left: "20px",
		right: "auto",
	},

	bubble: {
		width: "60px",
		height: "60px",
		borderRadius: "50%",
		border: "none",
		cursor: "pointer",
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
		transition: "all 0.3s ease",
		outline: "none",
		position: "relative" as const,
	},

	bubbleHover: {
		transform: "scale(1.05)",
		boxShadow: "0 6px 16px rgba(0, 0, 0, 0.2)",
	},

	chatWindow: {
		position: "absolute" as const,
		bottom: "70px",
		right: "0",
		width: "360px",
		height: "600px",
		maxHeight: "calc(100vh - 100px)",
		backgroundColor: "#ffffff",
		borderRadius: "12px",
		boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
		border: "1px solid #e5e7eb",
		display: "flex",
		flexDirection: "column" as const,
		overflow: "hidden",
		transform: "translateY(10px)",
		opacity: 0,
		transition: "all 0.3s ease",
		pointerEvents: "auto" as const,
	},

	chatWindowLeft: {
		left: "0",
		right: "auto",
	},

	chatWindowOpen: {
		transform: "translateY(0)",
		opacity: 1,
	},

	header: {
		padding: "16px 20px",
		borderBottom: "1px solid #e5e7eb",
		display: "flex",
		alignItems: "center",
		justifyContent: "space-between",
		backgroundColor: "#ffffff",
	},

	headerTitle: {
		margin: 0,
		fontSize: "16px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		fontWeight: "600",
		color: "#111827",
	},

	closeButton: {
		background: "none",
		border: "none",
		cursor: "pointer",
		padding: "4px",
		borderRadius: "4px",
		color: "#6b7280",
		fontSize: "18px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		lineHeight: 1,
		outline: "none",
	},

	messagesContainer: {
		flex: 1,
		padding: "16px",
		overflowY: "auto" as const,
		display: "flex",
		flexDirection: "column" as const,
		gap: "12px",
	},

	message: {
		maxWidth: "80%",
		padding: "8px 12px",
		borderRadius: "12px",
		fontSize: "14px",
		lineHeight: "1.4",
	},

	userMessage: {
		alignSelf: "flex-end" as const,
		backgroundColor: "#3b82f6",
		color: "#ffffff",
	},

	assistantMessage: {
		alignSelf: "flex-start" as const,
		backgroundColor: "#f3f4f6",
		color: "#111827",
	},

	inputContainer: {
		padding: "16px",
		borderTop: "1px solid #e5e7eb",
		display: "flex",
		gap: "8px",
		backgroundColor: "#ffffff",
		color: "#111827 !important",
	},

	input: {
		flex: 1,
		padding: "8px 12px",
		border: "1px solid #d1d5db",
		borderRadius: "8px",
		fontSize: "14px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		outline: "none",
		resize: "none" as const,
		minHeight: "36px",
		maxHeight: "100px",
		backgroundColor: "#ffffff",
	},

	sendButton: {
		padding: "8px 16px",
		border: "none",
		borderRadius: "8px",
		cursor: "pointer",
		fontSize: "14px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		fontWeight: "500",
		outline: "none",
		transition: "all 0.2s ease",
	},

	sendButtonDisabled: {
		opacity: 0.5,
		cursor: "not-allowed",
	},

	brandingFooter: {
		padding: "8px 16px",
		textAlign: "center" as const,
		borderTop: "1px solid #f3f4f6",
		backgroundColor: "#fafafa",
	},

	brandingText: {
		fontSize: "11px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		color: "#9ca3af",
		margin: 0,
	},

	brandingLink: {
		color: "#6b7280",
		textDecoration: "none",
		fontWeight: "500",
		transition: "color 0.2s ease",
	},

	// Markdown styles
	markdownContent: {
		lineHeight: "1.6",
	},
};

// Markdown Renderer Component
function MarkdownRenderer({
	content,
	primaryColor,
}: { content: string; primaryColor: string }) {
	return (
		<div style={styles.markdownContent}>
			<ReactMarkdown
				remarkPlugins={[remarkGfm]}
				components={{
					// Paragraphs and text
					p: ({ children }) => (
						<p style={{ margin: "8px 0", color: "inherit" }}>{children}</p>
					),
					// Lists
					ul: ({ children }) => (
						<ul
							style={{
								margin: "8px 0",
								paddingLeft: "20px",
								color: "inherit",
							}}
						>
							{children}
						</ul>
					),
					ol: ({ children }) => (
						<ol
							style={{
								margin: "8px 0",
								paddingLeft: "20px",
								color: "inherit",
							}}
						>
							{children}
						</ol>
					),
					li: ({ children }) => (
						<li style={{ margin: "4px 0", color: "inherit" }}>{children}</li>
					),
					// Code
					code: ({ children, className }) => {
						const isInline = !className;
						if (isInline) {
							return (
								<code
									style={{
										backgroundColor: "rgba(0, 0, 0, 0.1)",
										padding: "2px 4px",
										borderRadius: "3px",
										fontSize: "13px",
										fontFamily: "Monaco, Consolas, 'Courier New', monospace",
										color: "inherit",
									}}
								>
									{children}
								</code>
							);
						}
						return (
							<code
								style={{
									backgroundColor: "rgba(0, 0, 0, 0.1)",
									padding: "12px",
									borderRadius: "6px",
									overflow: "auto",
									margin: "8px 0",
									fontSize: "13px",
									fontFamily: "Monaco, Consolas, 'Courier New', monospace",
									color: "inherit",
									display: "block",
								}}
							>
								{children}
							</code>
						);
					},
					// Links
					a: ({ children, href }) => (
						<a
							href={href}
							style={{
								color: primaryColor,
								textDecoration: "underline",
							}}
							target="_blank"
							rel="noopener noreferrer"
						>
							{children}
						</a>
					),
					// Text formatting
					strong: ({ children }) => (
						<strong style={{ fontWeight: 600, color: "inherit" }}>
							{children}
						</strong>
					),
					em: ({ children }) => (
						<em style={{ fontStyle: "italic", color: "inherit" }}>
							{children}
						</em>
					),
				}}
			>
				{content}
			</ReactMarkdown>
		</div>
	);
}

// Main Widget Component
function Widget({ config, shadowRoot }: WidgetProps) {
	const [isOpen, setIsOpen] = useState(config.initiallyOpen || false);
	const [messages, setMessages] = useState<Message[]>([]);
	const [inputValue, setInputValue] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [isHovered, setIsHovered] = useState(false);
	const [currentConversationId, setCurrentConversationId] = useState<
		string | null
	>(config.conversationId || null);
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const inputRef = useRef<HTMLTextAreaElement>(null);

	// Colors with fallbacks
	const primaryColor = config.primaryColor || "#3b82f6";
	const secondaryColor = config.secondaryColor || "#f3f4f6";

	// Position styles
	const isLeft = config.position === "bottom-left";
	const containerStyle = {
		...styles.container,
		...(isLeft ? styles.containerLeft : {}),
	};
	const chatWindowStyle = {
		...styles.chatWindow,
		...(isLeft ? styles.chatWindowLeft : {}),
		...(isOpen ? styles.chatWindowOpen : {}),
	};

	// Auto-scroll to bottom when messages change
	useEffect(() => {
		if (messagesEndRef.current) {
			messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
		}
	});

	// Load conversation history on mount
	useEffect(() => {
		const loadConversationHistory = async () => {
			if (!currentConversationId) return;

			try {
				const baseUrl = config.baseUrl || "";
				const response = await fetch(
					`${baseUrl}/api/conversations/${currentConversationId}/messages`,
					{
						method: "GET",
						headers: {
							"Content-Type": "application/json",
						},
					},
				);

				if (response.ok) {
					const data = await response.json();
					if (data.messages && Array.isArray(data.messages)) {
						const formattedMessages: Message[] = data.messages.map(
							(msg: {
								id: string;
								content: string;
								role: string;
								createdAt: string;
							}) => ({
								id: msg.id,
								content: msg.content,
								role: msg.role,
								timestamp: new Date(msg.createdAt),
							}),
						);
						setMessages(formattedMessages);
					}
				}
			} catch (error) {
				console.error("[Widget] Error loading conversation history:", error);
			}
		};

		loadConversationHistory();
	}, [currentConversationId, config.baseUrl]);

	// Handle input changes with auto-resize
	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLTextAreaElement>) => {
			setInputValue(e.target.value);
			// Auto-resize textarea
			const textarea = e.target;
			textarea.style.height = "auto";
			textarea.style.height = `${Math.min(textarea.scrollHeight, 100)}px`;
		},
		[],
	);

	// Handle key press for sending messages
	const handleKeyPress = useCallback(
		(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
			if (e.key === "Enter" && !e.shiftKey) {
				e.preventDefault();
				if (inputValue.trim() && !isLoading) {
					handleSendMessage();
				}
			}
		},
		[inputValue, isLoading],
	);

	// Handle sending messages with proper content cleaning
	const handleSendMessage = useCallback(async () => {
		if (!inputValue.trim() || isLoading) return;

		const userMessage: Message = {
			id: `user-${Date.now()}`,
			content: inputValue.trim(),
			role: "user",
			timestamp: new Date(),
		};

		// Add user message immediately
		setMessages((prev) => [...prev, userMessage]);
		setInputValue("");
		setIsLoading(true);

		// Reset textarea height
		if (inputRef.current) {
			inputRef.current.style.height = "auto";
		}

		// Create assistant message placeholder
		const assistantMessageId = `assistant-${Date.now()}`;
		const assistantMessage: Message = {
			id: assistantMessageId,
			content: "Thinking...",
			role: "assistant",
			timestamp: new Date(),
		};

		setMessages((prev) => [...prev, assistantMessage]);

		try {
			const baseUrl = config.baseUrl || "";
			const response = await fetch(`${baseUrl}/api/chat/${config.websiteId}`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					message: inputValue.trim(),
					visitorId: config.visitorId,
					conversationId: currentConversationId,
				}),
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const reader = response.body?.getReader();
			if (!reader) {
				throw new Error("No response body");
			}

			let fullContent = "";

			// Process the streaming response
			while (true) {
				const { done, value } = await reader.read();
				if (done) break;

				const chunk = new TextDecoder().decode(value);
				const lines = chunk.split("\n");

				for (const line of lines) {
					if (line.trim() === "") continue;

					if (line.startsWith("0:")) {
						// Text content from AI SDK stream protocol
						try {
							const content = JSON.parse(line.slice(2));
							fullContent += content;

							// Hide "Thinking..." as soon as we get the first meaningful content
							if (isLoading && content.trim()) {
								setIsLoading(false);
							}

							// Clean the accumulated content to remove formatting artifacts
							const cleanContent = cleanStreamingContent(fullContent);

							// Update the assistant message with cleaned accumulated content
							setMessages((prev) =>
								prev.map((msg) =>
									msg.id === assistantMessageId
										? { ...msg, content: cleanContent }
										: msg,
								),
							);
						} catch (e) {
							console.warn("[Widget] Failed to parse text content:", e);
							// Fallback to raw content if JSON parsing fails
							const content = line.slice(2);
							fullContent += content;

							// Hide "Thinking..." as soon as we get the first meaningful content
							if (isLoading && content.trim()) {
								setIsLoading(false);
							}

							// Clean the accumulated content even in fallback case
							const cleanContent = cleanStreamingContent(fullContent);

							// Update the assistant message with cleaned accumulated content
							setMessages((prev) =>
								prev.map((msg) =>
									msg.id === assistantMessageId
										? { ...msg, content: cleanContent }
										: msg,
								),
							);
						}
					} else if (line.startsWith("8:")) {
						// Conversation ID from stream
						try {
							const data = JSON.parse(line.slice(2));
							if (data.conversationId && !currentConversationId) {
								setCurrentConversationId(data.conversationId);
								config.onConversationIdChange?.(data.conversationId);
							}
						} catch (e) {
							console.warn("[Widget] Failed to parse conversation data:", e);
						}
					}
				}
			}

			// If no content was received, show error
			if (!fullContent) {
				setMessages((prev) =>
					prev.map((msg) =>
						msg.id === assistantMessageId
							? { ...msg, content: "Sorry, I encountered an error." }
							: msg,
					),
				);
			}
		} catch (error) {
			console.error("Error sending message:", error);

			// Update the assistant message with error content
			setMessages((prev) =>
				prev.map((msg) =>
					msg.id === assistantMessageId
						? {
								...msg,
								content: "Sorry, I encountered an error. Please try again.",
							}
						: msg,
				),
			);
		} finally {
			// Always ensure loading is false when done
			setIsLoading(false);
		}
	}, [inputValue, isLoading, config, currentConversationId]);

	return (
		<div style={containerStyle}>
			{/* Chat Window */}
			{isOpen && (
				<div style={chatWindowStyle}>
					{/* Header */}
					<div style={styles.header}>
						<h3 style={styles.headerTitle}>
							{config.headerText || "Chat with us"}
						</h3>
						<button
							type="button"
							style={styles.closeButton}
							onClick={() => setIsOpen(false)}
							aria-label="Close chat"
						>
							×
						</button>
					</div>

					{/* Messages */}
					<div style={styles.messagesContainer}>
						{/* Welcome message */}
						{messages.length === 0 && config.welcomeMessage && (
							<div
								style={{
									...styles.message,
									...styles.assistantMessage,
								}}
							>
								<MarkdownRenderer
									content={config.welcomeMessage}
									primaryColor={primaryColor}
								/>
							</div>
						)}

						{/* Message list */}
						{messages.map((message) => (
							<div
								key={message.id}
								style={{
									...styles.message,
									...(message.role === "user"
										? styles.userMessage
										: styles.assistantMessage),
								}}
							>
								{message.role === "assistant" ? (
									<MarkdownRenderer
										content={message.content}
										primaryColor={primaryColor}
									/>
								) : (
									message.content
								)}
							</div>
						))}
						<div ref={messagesEndRef} />
					</div>

					{/* Input */}
					<div style={styles.inputContainer}>
						<textarea
							ref={inputRef}
							style={{
								...styles.input,
								color: "#111827",
							}}
							value={inputValue}
							onChange={handleInputChange}
							onKeyDown={handleKeyPress}
							placeholder="Type your message..."
							disabled={isLoading}
							rows={1}
						/>
						<button
							type="button"
							style={{
								...styles.sendButton,
								backgroundColor: primaryColor,
								color: "#ffffff",
								...(isLoading || !inputValue.trim()
									? styles.sendButtonDisabled
									: {}),
							}}
							onClick={handleSendMessage}
							disabled={isLoading || !inputValue.trim()}
						>
							Send
						</button>
					</div>

					{/* Branding Footer */}
					<div style={styles.brandingFooter}>
						<p style={styles.brandingText}>
							Powered by{" "}
							<a
								href="https://bublai.com"
								target="_blank"
								rel="noopener noreferrer"
								style={styles.brandingLink}
							>
								Bubl
							</a>
						</p>
					</div>
				</div>
			)}

			{/* Chat Bubble */}
			<button
				type="button"
				style={{
					...styles.bubble,
					backgroundColor: primaryColor,
					...(isHovered ? styles.bubbleHover : {}),
				}}
				onClick={() => setIsOpen(!isOpen)}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
				aria-label={isOpen ? "Close chat" : "Open chat"}
			>
				{isOpen ? (
					<span style={{ color: "#ffffff", fontSize: "24px" }}>×</span>
				) : (
					<span style={{ color: "#ffffff", fontSize: "24px" }}>💬</span>
				)}
			</button>
		</div>
	);
}

// Widget initialization function
export function initWidget(config: WidgetConfig): () => void {
	// Create shadow DOM container
	const container = document.createElement("div");
	container.id = "bubl-chat-widget";
	document.body.appendChild(container);

	// Create shadow root for complete style isolation
	const shadowRoot = container.attachShadow({ mode: "open" });

	// Create React root and render widget
	const root = createRoot(shadowRoot);
	root.render(<Widget config={config} shadowRoot={shadowRoot} />);

	// Return cleanup function
	return () => {
		root.unmount();
		container.remove();
	};
}

// Default export for the Widget component
export default Widget;
