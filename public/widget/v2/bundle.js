
/**
 * Bubl Widget V2 Bundle
 * Self-contained React widget with Shadow DOM isolation
 * Generated: 2025-06-03T10:14:39.858Z
 */

"use strict";(()=>{var Lx=Object.create;var da=Object.defineProperty,Ux=Object.defineProperties,Bx=Object.getOwnPropertyDescriptor,Hx=Object.getOwnPropertyDescriptors,qx=Object.getOwnPropertyNames,ha=Object.getOwnPropertySymbols,jx=Object.getPrototypeOf,Co=Object.prototype.hasOwnProperty,Jm=Object.prototype.propertyIsEnumerable;var Wm=Math.pow,Im=(t,e,n)=>e in t?da(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,C=(t,e)=>{for(var n in e||(e={}))Co.call(e,n)&&Im(t,n,e[n]);if(ha)for(var n of ha(e))Jm.call(e,n)&&Im(t,n,e[n]);return t},Nt=(t,e)=>Ux(t,Hx(e));var Pm=(t,e)=>{var n={};for(var l in t)Co.call(t,l)&&e.indexOf(l)<0&&(n[l]=t[l]);if(t!=null&&ha)for(var l of ha(t))e.indexOf(l)<0&&Jm.call(t,l)&&(n[l]=t[l]);return n};var Pt=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),$m=(t,e)=>{for(var n in e)da(t,n,{get:e[n],enumerable:!0})},Yx=(t,e,n,l)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of qx(e))!Co.call(t,i)&&i!==n&&da(t,i,{get:()=>e[i],enumerable:!(l=Bx(e,i))||l.enumerable});return t};var vn=(t,e,n)=>(n=t!=null?Lx(jx(t)):{},Yx(e||!t||!t.__esModule?da(n,"default",{value:t,enumerable:!0}):n,t));var Hl=(t,e,n)=>new Promise((l,i)=>{var r=o=>{try{u(n.next(o))}catch(c){i(c)}},a=o=>{try{u(n.throw(o))}catch(c){i(c)}},u=o=>o.done?l(o.value):Promise.resolve(o.value).then(r,a);u((n=n.apply(t,e)).next())});var fp=Pt(X=>{"use strict";var Mo=Symbol.for("react.transitional.element"),Vx=Symbol.for("react.portal"),Xx=Symbol.for("react.fragment"),Gx=Symbol.for("react.strict_mode"),Qx=Symbol.for("react.profiler"),Zx=Symbol.for("react.consumer"),Fx=Symbol.for("react.context"),Kx=Symbol.for("react.forward_ref"),Ix=Symbol.for("react.suspense"),Jx=Symbol.for("react.memo"),rp=Symbol.for("react.lazy"),tp=Symbol.iterator;function Wx(t){return t===null||typeof t!="object"?null:(t=tp&&t[tp]||t["@@iterator"],typeof t=="function"?t:null)}var ap={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},up=Object.assign,op={};function jl(t,e,n){this.props=t,this.context=e,this.refs=op,this.updater=n||ap}jl.prototype.isReactComponent={};jl.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};jl.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function cp(){}cp.prototype=jl.prototype;function Oo(t,e,n){this.props=t,this.context=e,this.refs=op,this.updater=n||ap}var _o=Oo.prototype=new cp;_o.constructor=Oo;up(_o,jl.prototype);_o.isPureReactComponent=!0;var ep=Array.isArray,ft={H:null,A:null,T:null,S:null,V:null},sp=Object.prototype.hasOwnProperty;function Ro(t,e,n,l,i,r){return n=r.ref,{$$typeof:Mo,type:t,key:e,ref:n!==void 0?n:null,props:r}}function Px(t,e){return Ro(t.type,e,void 0,void 0,void 0,t.props)}function No(t){return typeof t=="object"&&t!==null&&t.$$typeof===Mo}function $x(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var np=/\/+/g;function Do(t,e){return typeof t=="object"&&t!==null&&t.key!=null?$x(""+t.key):e.toString(36)}function lp(){}function tb(t){switch(t.status){case"fulfilled":return t.value;case"rejected":throw t.reason;default:switch(typeof t.status=="string"?t.then(lp,lp):(t.status="pending",t.then(function(e){t.status==="pending"&&(t.status="fulfilled",t.value=e)},function(e){t.status==="pending"&&(t.status="rejected",t.reason=e)})),t.status){case"fulfilled":return t.value;case"rejected":throw t.reason}}throw t}function ql(t,e,n,l,i){var r=typeof t;(r==="undefined"||r==="boolean")&&(t=null);var a=!1;if(t===null)a=!0;else switch(r){case"bigint":case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case Mo:case Vx:a=!0;break;case rp:return a=t._init,ql(a(t._payload),e,n,l,i)}}if(a)return i=i(t),a=l===""?"."+Do(t,0):l,ep(i)?(n="",a!=null&&(n=a.replace(np,"$&/")+"/"),ql(i,e,n,"",function(c){return c})):i!=null&&(No(i)&&(i=Px(i,n+(i.key==null||t&&t.key===i.key?"":(""+i.key).replace(np,"$&/")+"/")+a)),e.push(i)),1;a=0;var u=l===""?".":l+":";if(ep(t))for(var o=0;o<t.length;o++)l=t[o],r=u+Do(l,o),a+=ql(l,e,n,r,i);else if(o=Wx(t),typeof o=="function")for(t=o.call(t),o=0;!(l=t.next()).done;)l=l.value,r=u+Do(l,o++),a+=ql(l,e,n,r,i);else if(r==="object"){if(typeof t.then=="function")return ql(tb(t),e,n,l,i);throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.")}return a}function ga(t,e,n){if(t==null)return t;var l=[],i=0;return ql(t,l,"","",function(r){return e.call(n,r,i++)}),l}function eb(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var ip=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function nb(){}X.Children={map:ga,forEach:function(t,e,n){ga(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return ga(t,function(){e++}),e},toArray:function(t){return ga(t,function(e){return e})||[]},only:function(t){if(!No(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};X.Component=jl;X.Fragment=Xx;X.Profiler=Qx;X.PureComponent=Oo;X.StrictMode=Gx;X.Suspense=Ix;X.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ft;X.__COMPILER_RUNTIME={__proto__:null,c:function(t){return ft.H.useMemoCache(t)}};X.cache=function(t){return function(){return t.apply(null,arguments)}};X.cloneElement=function(t,e,n){if(t==null)throw Error("The argument must be a React element, but you passed "+t+".");var l=up({},t.props),i=t.key,r=void 0;if(e!=null)for(a in e.ref!==void 0&&(r=void 0),e.key!==void 0&&(i=""+e.key),e)!sp.call(e,a)||a==="key"||a==="__self"||a==="__source"||a==="ref"&&e.ref===void 0||(l[a]=e[a]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var u=Array(a),o=0;o<a;o++)u[o]=arguments[o+2];l.children=u}return Ro(t.type,i,void 0,void 0,r,l)};X.createContext=function(t){return t={$$typeof:Fx,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null},t.Provider=t,t.Consumer={$$typeof:Zx,_context:t},t};X.createElement=function(t,e,n){var l,i={},r=null;if(e!=null)for(l in e.key!==void 0&&(r=""+e.key),e)sp.call(e,l)&&l!=="key"&&l!=="__self"&&l!=="__source"&&(i[l]=e[l]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var u=Array(a),o=0;o<a;o++)u[o]=arguments[o+2];i.children=u}if(t&&t.defaultProps)for(l in a=t.defaultProps,a)i[l]===void 0&&(i[l]=a[l]);return Ro(t,r,void 0,void 0,null,i)};X.createRef=function(){return{current:null}};X.forwardRef=function(t){return{$$typeof:Kx,render:t}};X.isValidElement=No;X.lazy=function(t){return{$$typeof:rp,_payload:{_status:-1,_result:t},_init:eb}};X.memo=function(t,e){return{$$typeof:Jx,type:t,compare:e===void 0?null:e}};X.startTransition=function(t){var e=ft.T,n={};ft.T=n;try{var l=t(),i=ft.S;i!==null&&i(n,l),typeof l=="object"&&l!==null&&typeof l.then=="function"&&l.then(nb,ip)}catch(r){ip(r)}finally{ft.T=e}};X.unstable_useCacheRefresh=function(){return ft.H.useCacheRefresh()};X.use=function(t){return ft.H.use(t)};X.useActionState=function(t,e,n){return ft.H.useActionState(t,e,n)};X.useCallback=function(t,e){return ft.H.useCallback(t,e)};X.useContext=function(t){return ft.H.useContext(t)};X.useDebugValue=function(){};X.useDeferredValue=function(t,e){return ft.H.useDeferredValue(t,e)};X.useEffect=function(t,e,n){var l=ft.H;if(typeof n=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return l.useEffect(t,e)};X.useId=function(){return ft.H.useId()};X.useImperativeHandle=function(t,e,n){return ft.H.useImperativeHandle(t,e,n)};X.useInsertionEffect=function(t,e){return ft.H.useInsertionEffect(t,e)};X.useLayoutEffect=function(t,e){return ft.H.useLayoutEffect(t,e)};X.useMemo=function(t,e){return ft.H.useMemo(t,e)};X.useOptimistic=function(t,e){return ft.H.useOptimistic(t,e)};X.useReducer=function(t,e,n){return ft.H.useReducer(t,e,n)};X.useRef=function(t){return ft.H.useRef(t)};X.useState=function(t){return ft.H.useState(t)};X.useSyncExternalStore=function(t,e,n){return ft.H.useSyncExternalStore(t,e,n)};X.useTransition=function(){return ft.H.useTransition()};X.version="19.1.0"});var Qi=Pt((fA,mp)=>{"use strict";mp.exports=fp()});var kp=Pt(mt=>{"use strict";function Ho(t,e){var n=t.length;t.push(e);t:for(;0<n;){var l=n-1>>>1,i=t[l];if(0<ya(i,e))t[l]=e,t[n]=i,n=l;else break t}}function He(t){return t.length===0?null:t[0]}function ba(t){if(t.length===0)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var l=0,i=t.length,r=i>>>1;l<r;){var a=2*(l+1)-1,u=t[a],o=a+1,c=t[o];if(0>ya(u,n))o<i&&0>ya(c,u)?(t[l]=c,t[o]=n,l=o):(t[l]=u,t[a]=n,l=a);else if(o<i&&0>ya(c,n))t[l]=c,t[o]=n,l=o;else break t}}return e}function ya(t,e){var n=t.sortIndex-e.sortIndex;return n!==0?n:t.id-e.id}mt.unstable_now=void 0;typeof performance=="object"&&typeof performance.now=="function"?(pp=performance,mt.unstable_now=function(){return pp.now()}):(Lo=Date,hp=Lo.now(),mt.unstable_now=function(){return Lo.now()-hp});var pp,Lo,hp,$e=[],Sn=[],lb=1,ve=null,Ft=3,qo=!1,Zi=!1,Fi=!1,jo=!1,yp=typeof setTimeout=="function"?setTimeout:null,xp=typeof clearTimeout=="function"?clearTimeout:null,dp=typeof setImmediate!="undefined"?setImmediate:null;function xa(t){for(var e=He(Sn);e!==null;){if(e.callback===null)ba(Sn);else if(e.startTime<=t)ba(Sn),e.sortIndex=e.expirationTime,Ho($e,e);else break;e=He(Sn)}}function Yo(t){if(Fi=!1,xa(t),!Zi)if(He($e)!==null)Zi=!0,Vl||(Vl=!0,Yl());else{var e=He(Sn);e!==null&&Vo(Yo,e.startTime-t)}}var Vl=!1,Ki=-1,bp=5,vp=-1;function Sp(){return jo?!0:!(mt.unstable_now()-vp<bp)}function Uo(){if(jo=!1,Vl){var t=mt.unstable_now();vp=t;var e=!0;try{t:{Zi=!1,Fi&&(Fi=!1,xp(Ki),Ki=-1),qo=!0;var n=Ft;try{e:{for(xa(t),ve=He($e);ve!==null&&!(ve.expirationTime>t&&Sp());){var l=ve.callback;if(typeof l=="function"){ve.callback=null,Ft=ve.priorityLevel;var i=l(ve.expirationTime<=t);if(t=mt.unstable_now(),typeof i=="function"){ve.callback=i,xa(t),e=!0;break e}ve===He($e)&&ba($e),xa(t)}else ba($e);ve=He($e)}if(ve!==null)e=!0;else{var r=He(Sn);r!==null&&Vo(Yo,r.startTime-t),e=!1}}break t}finally{ve=null,Ft=n,qo=!1}e=void 0}}finally{e?Yl():Vl=!1}}}var Yl;typeof dp=="function"?Yl=function(){dp(Uo)}:typeof MessageChannel!="undefined"?(Bo=new MessageChannel,gp=Bo.port2,Bo.port1.onmessage=Uo,Yl=function(){gp.postMessage(null)}):Yl=function(){yp(Uo,0)};var Bo,gp;function Vo(t,e){Ki=yp(function(){t(mt.unstable_now())},e)}mt.unstable_IdlePriority=5;mt.unstable_ImmediatePriority=1;mt.unstable_LowPriority=4;mt.unstable_NormalPriority=3;mt.unstable_Profiling=null;mt.unstable_UserBlockingPriority=2;mt.unstable_cancelCallback=function(t){t.callback=null};mt.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):bp=0<t?Math.floor(1e3/t):5};mt.unstable_getCurrentPriorityLevel=function(){return Ft};mt.unstable_next=function(t){switch(Ft){case 1:case 2:case 3:var e=3;break;default:e=Ft}var n=Ft;Ft=e;try{return t()}finally{Ft=n}};mt.unstable_requestPaint=function(){jo=!0};mt.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=Ft;Ft=t;try{return e()}finally{Ft=n}};mt.unstable_scheduleCallback=function(t,e,n){var l=mt.unstable_now();switch(typeof n=="object"&&n!==null?(n=n.delay,n=typeof n=="number"&&0<n?l+n:l):n=l,t){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return i=n+i,t={id:lb++,callback:e,priorityLevel:t,startTime:n,expirationTime:i,sortIndex:-1},n>l?(t.sortIndex=n,Ho(Sn,t),He($e)===null&&t===He(Sn)&&(Fi?(xp(Ki),Ki=-1):Fi=!0,Vo(Yo,n-l))):(t.sortIndex=i,Ho($e,t),Zi||qo||(Zi=!0,Vl||(Vl=!0,Yl()))),t};mt.unstable_shouldYield=Sp;mt.unstable_wrapCallback=function(t){var e=Ft;return function(){var n=Ft;Ft=e;try{return t.apply(this,arguments)}finally{Ft=n}}}});var Tp=Pt((pA,Ep)=>{"use strict";Ep.exports=kp()});var wp=Pt(te=>{"use strict";var ib=Qi();function Ap(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function kn(){}var $t={d:{f:kn,r:function(){throw Error(Ap(522))},D:kn,C:kn,L:kn,m:kn,X:kn,S:kn,M:kn},p:0,findDOMNode:null},rb=Symbol.for("react.portal");function ab(t,e,n){var l=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:rb,key:l==null?null:""+l,children:t,containerInfo:e,implementation:n}}var Ii=ib.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function va(t,e){if(t==="font")return"";if(typeof e=="string")return e==="use-credentials"?e:""}te.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=$t;te.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)throw Error(Ap(299));return ab(t,e,null,n)};te.flushSync=function(t){var e=Ii.T,n=$t.p;try{if(Ii.T=null,$t.p=2,t)return t()}finally{Ii.T=e,$t.p=n,$t.d.f()}};te.preconnect=function(t,e){typeof t=="string"&&(e?(e=e.crossOrigin,e=typeof e=="string"?e==="use-credentials"?e:"":void 0):e=null,$t.d.C(t,e))};te.prefetchDNS=function(t){typeof t=="string"&&$t.d.D(t)};te.preinit=function(t,e){if(typeof t=="string"&&e&&typeof e.as=="string"){var n=e.as,l=va(n,e.crossOrigin),i=typeof e.integrity=="string"?e.integrity:void 0,r=typeof e.fetchPriority=="string"?e.fetchPriority:void 0;n==="style"?$t.d.S(t,typeof e.precedence=="string"?e.precedence:void 0,{crossOrigin:l,integrity:i,fetchPriority:r}):n==="script"&&$t.d.X(t,{crossOrigin:l,integrity:i,fetchPriority:r,nonce:typeof e.nonce=="string"?e.nonce:void 0})}};te.preinitModule=function(t,e){if(typeof t=="string")if(typeof e=="object"&&e!==null){if(e.as==null||e.as==="script"){var n=va(e.as,e.crossOrigin);$t.d.M(t,{crossOrigin:n,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0})}}else e==null&&$t.d.M(t)};te.preload=function(t,e){if(typeof t=="string"&&typeof e=="object"&&e!==null&&typeof e.as=="string"){var n=e.as,l=va(n,e.crossOrigin);$t.d.L(t,n,{crossOrigin:l,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0,type:typeof e.type=="string"?e.type:void 0,fetchPriority:typeof e.fetchPriority=="string"?e.fetchPriority:void 0,referrerPolicy:typeof e.referrerPolicy=="string"?e.referrerPolicy:void 0,imageSrcSet:typeof e.imageSrcSet=="string"?e.imageSrcSet:void 0,imageSizes:typeof e.imageSizes=="string"?e.imageSizes:void 0,media:typeof e.media=="string"?e.media:void 0})}};te.preloadModule=function(t,e){if(typeof t=="string")if(e){var n=va(e.as,e.crossOrigin);$t.d.m(t,{as:typeof e.as=="string"&&e.as!=="script"?e.as:void 0,crossOrigin:n,integrity:typeof e.integrity=="string"?e.integrity:void 0})}else $t.d.m(t)};te.requestFormReset=function(t){$t.d.r(t)};te.unstable_batchedUpdates=function(t,e){return t(e)};te.useFormState=function(t,e,n){return Ii.H.useFormState(t,e,n)};te.useFormStatus=function(){return Ii.H.useHostTransitionStatus()};te.version="19.1.0"});var Dp=Pt((dA,Cp)=>{"use strict";function zp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(zp)}catch(t){console.error(t)}}zp(),Cp.exports=wp()});var Oy=Pt(Vu=>{"use strict";var _t=Tp(),Wh=Qi(),ub=Dp();function w(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Ph(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Ur(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function $h(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function Mp(t){if(Ur(t)!==t)throw Error(w(188))}function ob(t){var e=t.alternate;if(!e){if(e=Ur(t),e===null)throw Error(w(188));return e!==t?null:t}for(var n=t,l=e;;){var i=n.return;if(i===null)break;var r=i.alternate;if(r===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===n)return Mp(i),t;if(r===l)return Mp(i),e;r=r.sibling}throw Error(w(188))}if(n.return!==l.return)n=i,l=r;else{for(var a=!1,u=i.child;u;){if(u===n){a=!0,n=i,l=r;break}if(u===l){a=!0,l=i,n=r;break}u=u.sibling}if(!a){for(u=r.child;u;){if(u===n){a=!0,n=r,l=i;break}if(u===l){a=!0,l=r,n=i;break}u=u.sibling}if(!a)throw Error(w(189))}}if(n.alternate!==l)throw Error(w(190))}if(n.tag!==3)throw Error(w(188));return n.stateNode.current===n?t:e}function td(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=td(t),e!==null)return e;t=t.sibling}return null}var st=Object.assign,cb=Symbol.for("react.element"),Sa=Symbol.for("react.transitional.element"),ir=Symbol.for("react.portal"),Il=Symbol.for("react.fragment"),ed=Symbol.for("react.strict_mode"),vc=Symbol.for("react.profiler"),sb=Symbol.for("react.provider"),nd=Symbol.for("react.consumer"),rn=Symbol.for("react.context"),ds=Symbol.for("react.forward_ref"),Sc=Symbol.for("react.suspense"),kc=Symbol.for("react.suspense_list"),gs=Symbol.for("react.memo"),An=Symbol.for("react.lazy");Symbol.for("react.scope");var Ec=Symbol.for("react.activity");Symbol.for("react.legacy_hidden");Symbol.for("react.tracing_marker");var fb=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var Op=Symbol.iterator;function Ji(t){return t===null||typeof t!="object"?null:(t=Op&&t[Op]||t["@@iterator"],typeof t=="function"?t:null)}var mb=Symbol.for("react.client.reference");function Tc(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===mb?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case Il:return"Fragment";case vc:return"Profiler";case ed:return"StrictMode";case Sc:return"Suspense";case kc:return"SuspenseList";case Ec:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case ir:return"Portal";case rn:return(t.displayName||"Context")+".Provider";case nd:return(t._context.displayName||"Context")+".Consumer";case ds:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case gs:return e=t.displayName||null,e!==null?e:Tc(t.type)||"Memo";case An:e=t._payload,t=t._init;try{return Tc(t(e))}catch(n){}}return null}var rr=Array.isArray,H=Wh.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,tt=ub.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ol={pending:!1,data:null,method:null,action:null},Ac=[],Jl=-1;function Qe(t){return{current:t}}function Ht(t){0>Jl||(t.current=Ac[Jl],Ac[Jl]=null,Jl--)}function ht(t,e){Jl++,Ac[Jl]=t.current,t.current=e}var Ve=Qe(null),kr=Qe(null),Ln=Qe(null),Wa=Qe(null);function Pa(t,e){switch(ht(Ln,e),ht(kr,t),ht(Ve,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Bh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Bh(e),t=by(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Ht(Ve),ht(Ve,t)}function di(){Ht(Ve),Ht(kr),Ht(Ln)}function wc(t){t.memoizedState!==null&&ht(Wa,t);var e=Ve.current,n=by(e,t.type);e!==n&&(ht(kr,t),ht(Ve,n))}function $a(t){kr.current===t&&(Ht(Ve),Ht(kr)),Wa.current===t&&(Ht(Wa),_r._currentValue=ol)}var zc=Object.prototype.hasOwnProperty,ys=_t.unstable_scheduleCallback,Xo=_t.unstable_cancelCallback,pb=_t.unstable_shouldYield,hb=_t.unstable_requestPaint,Xe=_t.unstable_now,db=_t.unstable_getCurrentPriorityLevel,ld=_t.unstable_ImmediatePriority,id=_t.unstable_UserBlockingPriority,tu=_t.unstable_NormalPriority,gb=_t.unstable_LowPriority,rd=_t.unstable_IdlePriority,yb=_t.log,xb=_t.unstable_setDisableYieldValue,Br=null,de=null;function On(t){if(typeof yb=="function"&&xb(t),de&&typeof de.setStrictMode=="function")try{de.setStrictMode(Br,t)}catch(e){}}var ge=Math.clz32?Math.clz32:Sb,bb=Math.log,vb=Math.LN2;function Sb(t){return t>>>=0,t===0?32:31-(bb(t)/vb|0)|0}var ka=256,Ea=4194304;function rl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function zu(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var i=0,r=t.suspendedLanes,a=t.pingedLanes;t=t.warmLanes;var u=l&134217727;return u!==0?(l=u&~r,l!==0?i=rl(l):(a&=u,a!==0?i=rl(a):n||(n=u&~t,n!==0&&(i=rl(n))))):(u=l&~r,u!==0?i=rl(u):a!==0?i=rl(a):n||(n=l&~t,n!==0&&(i=rl(n)))),i===0?0:e!==0&&e!==i&&!(e&r)&&(r=i&-i,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:i}function Hr(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function kb(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ad(){var t=ka;return ka<<=1,!(ka&4194048)&&(ka=256),t}function ud(){var t=Ea;return Ea<<=1,!(Ea&62914560)&&(Ea=4194304),t}function Go(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function qr(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Eb(t,e,n,l,i,r){var a=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var u=t.entanglements,o=t.expirationTimes,c=t.hiddenUpdates;for(n=a&~n;0<n;){var s=31-ge(n),f=1<<s;u[s]=0,o[s]=-1;var p=c[s];if(p!==null)for(c[s]=null,s=0;s<p.length;s++){var m=p[s];m!==null&&(m.lane&=-536870913)}n&=~f}l!==0&&od(t,l,0),r!==0&&i===0&&t.tag!==0&&(t.suspendedLanes|=r&~(a&~e))}function od(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-ge(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function cd(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-ge(n),i=1<<l;i&e|t[l]&e&&(t[l]|=e),n&=~i}}function xs(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function bs(t){return t&=-t,2<t?8<t?t&134217727?32:268435456:8:2}function sd(){var t=tt.p;return t!==0?t:(t=window.event,t===void 0?32:Dy(t.type))}function Tb(t,e){var n=tt.p;try{return tt.p=t,e()}finally{tt.p=n}}var Zn=Math.random().toString(36).slice(2),Kt="__reactFiber$"+Zn,ae="__reactProps$"+Zn,wi="__reactContainer$"+Zn,Cc="__reactEvents$"+Zn,Ab="__reactListeners$"+Zn,wb="__reactHandles$"+Zn,_p="__reactResources$"+Zn,jr="__reactMarker$"+Zn;function vs(t){delete t[Kt],delete t[ae],delete t[Cc],delete t[Ab],delete t[wb]}function Wl(t){var e=t[Kt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[wi]||n[Kt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=jh(t);t!==null;){if(n=t[Kt])return n;t=jh(t)}return e}t=n,n=t.parentNode}return null}function zi(t){if(t=t[Kt]||t[wi]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function ar(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(w(33))}function ui(t){var e=t[_p];return e||(e=t[_p]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Ut(t){t[jr]=!0}var fd=new Set,md={};function bl(t,e){gi(t,e),gi(t+"Capture",e)}function gi(t,e){for(md[t]=e,t=0;t<e.length;t++)fd.add(e[t])}var zb=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Rp={},Np={};function Cb(t){return zc.call(Np,t)?!0:zc.call(Rp,t)?!1:zb.test(t)?Np[t]=!0:(Rp[t]=!0,!1)}function Ha(t,e,n){if(Cb(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Ta(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function tn(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var Qo,Lp;function Zl(t){if(Qo===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Qo=e&&e[1]||"",Lp=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Qo+t+Lp}var Zo=!1;function Fo(t,e){if(!t||Zo)return"";Zo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var f=function(){throw Error()};if(Object.defineProperty(f.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(f,[])}catch(m){var p=m}Reflect.construct(t,[],f)}else{try{f.call()}catch(m){p=m}t.call(f.prototype)}}else{try{throw Error()}catch(m){p=m}(f=t())&&typeof f.catch=="function"&&f.catch(function(){})}}catch(m){if(m&&p&&typeof m.stack=="string")return[m.stack,p.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),a=r[0],u=r[1];if(a&&u){var o=a.split(`
`),c=u.split(`
`);for(i=l=0;l<o.length&&!o[l].includes("DetermineComponentFrameRoot");)l++;for(;i<c.length&&!c[i].includes("DetermineComponentFrameRoot");)i++;if(l===o.length||i===c.length)for(l=o.length-1,i=c.length-1;1<=l&&0<=i&&o[l]!==c[i];)i--;for(;1<=l&&0<=i;l--,i--)if(o[l]!==c[i]){if(l!==1||i!==1)do if(l--,i--,0>i||o[l]!==c[i]){var s=`
`+o[l].replace(" at new "," at ");return t.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",t.displayName)),s}while(1<=l&&0<=i);break}}}finally{Zo=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Zl(n):""}function Db(t){switch(t.tag){case 26:case 27:case 5:return Zl(t.type);case 16:return Zl("Lazy");case 13:return Zl("Suspense");case 19:return Zl("SuspenseList");case 0:case 15:return Fo(t.type,!1);case 11:return Fo(t.type.render,!1);case 1:return Fo(t.type,!0);case 31:return Zl("Activity");default:return""}}function Up(t){try{var e="";do e+=Db(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function ke(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function pd(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Mb(t){var e=pd(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(a){l=""+a,r.call(this,a)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(a){l=""+a},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function eu(t){t._valueTracker||(t._valueTracker=Mb(t))}function hd(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=pd(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function nu(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}var Ob=/[\n"\\]/g;function Ae(t){return t.replace(Ob,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Dc(t,e,n,l,i,r,a,u){t.name="",a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"?t.type=a:t.removeAttribute("type"),e!=null?a==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ke(e)):t.value!==""+ke(e)&&(t.value=""+ke(e)):a!=="submit"&&a!=="reset"||t.removeAttribute("value"),e!=null?Mc(t,a,ke(e)):n!=null?Mc(t,a,ke(n)):l!=null&&t.removeAttribute("value"),i==null&&r!=null&&(t.defaultChecked=!!r),i!=null&&(t.checked=i&&typeof i!="function"&&typeof i!="symbol"),u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"?t.name=""+ke(u):t.removeAttribute("name")}function dd(t,e,n,l,i,r,a,u){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+ke(n):"",e=e!=null?""+ke(e):n,u||e===t.value||(t.value=e),t.defaultValue=e}l=l!=null?l:i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=u?t.checked:!!l,t.defaultChecked=!!l,a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"&&(t.name=a)}function Mc(t,e,n){e==="number"&&nu(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function oi(t,e,n,l){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&l&&(t[n].defaultSelected=!0)}else{for(n=""+ke(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,l&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function gd(t,e,n){if(e!=null&&(e=""+ke(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+ke(n):""}function yd(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(w(92));if(rr(l)){if(1<l.length)throw Error(w(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=ke(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function yi(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var _b=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Bp(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||_b.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function xd(t,e,n){if(e!=null&&typeof e!="object")throw Error(w(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var i in e)l=e[i],e.hasOwnProperty(i)&&n[i]!==l&&Bp(t,i,l)}else for(var r in e)e.hasOwnProperty(r)&&Bp(t,r,e[r])}function Ss(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Rb=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Nb=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function qa(t){return Nb.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Oc=null;function ks(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Pl=null,ci=null;function Hp(t){var e=zi(t);if(e&&(t=e.stateNode)){var n=t[ae]||null;t:switch(t=e.stateNode,e.type){case"input":if(Dc(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ae(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var i=l[ae]||null;if(!i)throw Error(w(90));Dc(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&hd(l)}break t;case"textarea":gd(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&oi(t,!!n.multiple,e,!1)}}}var Ko=!1;function bd(t,e,n){if(Ko)return t(e,n);Ko=!0;try{var l=t(e);return l}finally{if(Ko=!1,(Pl!==null||ci!==null)&&(Bu(),Pl&&(e=Pl,t=ci,ci=Pl=null,Hp(e),t)))for(e=0;e<t.length;e++)Hp(t[e])}}function Er(t,e){var n=t.stateNode;if(n===null)return null;var l=n[ae]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(w(231,e,typeof n));return n}var mn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),_c=!1;if(mn)try{Xl={},Object.defineProperty(Xl,"passive",{get:function(){_c=!0}}),window.addEventListener("test",Xl,Xl),window.removeEventListener("test",Xl,Xl)}catch(t){_c=!1}var Xl,_n=null,Es=null,ja=null;function vd(){if(ja)return ja;var t,e=Es,n=e.length,l,i="value"in _n?_n.value:_n.textContent,r=i.length;for(t=0;t<n&&e[t]===i[t];t++);var a=n-t;for(l=1;l<=a&&e[n-l]===i[r-l];l++);return ja=i.slice(t,1<l?1-l:void 0)}function Ya(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Aa(){return!0}function qp(){return!1}function ue(t){function e(n,l,i,r,a){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=r,this.target=a,this.currentTarget=null;for(var u in t)t.hasOwnProperty(u)&&(n=t[u],this[u]=n?n(r):r[u]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Aa:qp,this.isPropagationStopped=qp,this}return st(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Aa)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Aa)},persist:function(){},isPersistent:Aa}),e}var vl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Cu=ue(vl),Yr=st({},vl,{view:0,detail:0}),Lb=ue(Yr),Io,Jo,Wi,Du=st({},Yr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ts,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Wi&&(Wi&&t.type==="mousemove"?(Io=t.screenX-Wi.screenX,Jo=t.screenY-Wi.screenY):Jo=Io=0,Wi=t),Io)},movementY:function(t){return"movementY"in t?t.movementY:Jo}}),jp=ue(Du),Ub=st({},Du,{dataTransfer:0}),Bb=ue(Ub),Hb=st({},Yr,{relatedTarget:0}),Wo=ue(Hb),qb=st({},vl,{animationName:0,elapsedTime:0,pseudoElement:0}),jb=ue(qb),Yb=st({},vl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Vb=ue(Yb),Xb=st({},vl,{data:0}),Yp=ue(Xb),Gb={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Qb={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Zb={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Fb(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Zb[t])?!!e[t]:!1}function Ts(){return Fb}var Kb=st({},Yr,{key:function(t){if(t.key){var e=Gb[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ya(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Qb[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ts,charCode:function(t){return t.type==="keypress"?Ya(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ya(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Ib=ue(Kb),Jb=st({},Du,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Vp=ue(Jb),Wb=st({},Yr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ts}),Pb=ue(Wb),$b=st({},vl,{propertyName:0,elapsedTime:0,pseudoElement:0}),tv=ue($b),ev=st({},Du,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),nv=ue(ev),lv=st({},vl,{newState:0,oldState:0}),iv=ue(lv),rv=[9,13,27,32],As=mn&&"CompositionEvent"in window,or=null;mn&&"documentMode"in document&&(or=document.documentMode);var av=mn&&"TextEvent"in window&&!or,Sd=mn&&(!As||or&&8<or&&11>=or),Xp=" ",Gp=!1;function kd(t,e){switch(t){case"keyup":return rv.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ed(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var $l=!1;function uv(t,e){switch(t){case"compositionend":return Ed(e);case"keypress":return e.which!==32?null:(Gp=!0,Xp);case"textInput":return t=e.data,t===Xp&&Gp?null:t;default:return null}}function ov(t,e){if($l)return t==="compositionend"||!As&&kd(t,e)?(t=vd(),ja=Es=_n=null,$l=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Sd&&e.locale!=="ko"?null:e.data;default:return null}}var cv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qp(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!cv[t.type]:e==="textarea"}function Td(t,e,n,l){Pl?ci?ci.push(l):ci=[l]:Pl=l,e=vu(e,"onChange"),0<e.length&&(n=new Cu("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var cr=null,Tr=null;function sv(t){gy(t,0)}function Mu(t){var e=ar(t);if(hd(e))return t}function Zp(t,e){if(t==="change")return e}var Ad=!1;mn&&(mn?(za="oninput"in document,za||(Po=document.createElement("div"),Po.setAttribute("oninput","return;"),za=typeof Po.oninput=="function"),wa=za):wa=!1,Ad=wa&&(!document.documentMode||9<document.documentMode));var wa,za,Po;function Fp(){cr&&(cr.detachEvent("onpropertychange",wd),Tr=cr=null)}function wd(t){if(t.propertyName==="value"&&Mu(Tr)){var e=[];Td(e,Tr,t,ks(t)),bd(sv,e)}}function fv(t,e,n){t==="focusin"?(Fp(),cr=e,Tr=n,cr.attachEvent("onpropertychange",wd)):t==="focusout"&&Fp()}function mv(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Mu(Tr)}function pv(t,e){if(t==="click")return Mu(e)}function hv(t,e){if(t==="input"||t==="change")return Mu(e)}function dv(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var be=typeof Object.is=="function"?Object.is:dv;function Ar(t,e){if(be(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!zc.call(e,i)||!be(t[i],e[i]))return!1}return!0}function Kp(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Ip(t,e){var n=Kp(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Kp(n)}}function zd(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?zd(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Cd(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=nu(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch(l){n=!1}if(n)t=e.contentWindow;else break;e=nu(t.document)}return e}function ws(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var gv=mn&&"documentMode"in document&&11>=document.documentMode,ti=null,Rc=null,sr=null,Nc=!1;function Jp(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Nc||ti==null||ti!==nu(l)||(l=ti,"selectionStart"in l&&ws(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),sr&&Ar(sr,l)||(sr=l,l=vu(Rc,"onSelect"),0<l.length&&(e=new Cu("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=ti)))}function il(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var ei={animationend:il("Animation","AnimationEnd"),animationiteration:il("Animation","AnimationIteration"),animationstart:il("Animation","AnimationStart"),transitionrun:il("Transition","TransitionRun"),transitionstart:il("Transition","TransitionStart"),transitioncancel:il("Transition","TransitionCancel"),transitionend:il("Transition","TransitionEnd")},$o={},Dd={};mn&&(Dd=document.createElement("div").style,"AnimationEvent"in window||(delete ei.animationend.animation,delete ei.animationiteration.animation,delete ei.animationstart.animation),"TransitionEvent"in window||delete ei.transitionend.transition);function Sl(t){if($o[t])return $o[t];if(!ei[t])return t;var e=ei[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Dd)return $o[t]=e[n];return t}var Md=Sl("animationend"),Od=Sl("animationiteration"),_d=Sl("animationstart"),yv=Sl("transitionrun"),xv=Sl("transitionstart"),bv=Sl("transitioncancel"),Rd=Sl("transitionend"),Nd=new Map,Lc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Lc.push("scrollEnd");function Ue(t,e){Nd.set(t,e),bl(e,[t])}var Wp=new WeakMap;function we(t,e){if(typeof t=="object"&&t!==null){var n=Wp.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Up(e)},Wp.set(t,e),e)}return{value:t,source:e,stack:Up(e)}}var Se=[],ni=0,zs=0;function Ou(){for(var t=ni,e=zs=ni=0;e<t;){var n=Se[e];Se[e++]=null;var l=Se[e];Se[e++]=null;var i=Se[e];Se[e++]=null;var r=Se[e];if(Se[e++]=null,l!==null&&i!==null){var a=l.pending;a===null?i.next=i:(i.next=a.next,a.next=i),l.pending=i}r!==0&&Ld(n,i,r)}}function _u(t,e,n,l){Se[ni++]=t,Se[ni++]=e,Se[ni++]=n,Se[ni++]=l,zs|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Cs(t,e,n,l){return _u(t,e,n,l),lu(t)}function Ci(t,e){return _u(t,null,null,e),lu(t)}function Ld(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var i=!1,r=t.return;r!==null;)r.childLanes|=n,l=r.alternate,l!==null&&(l.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(i=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,i&&e!==null&&(i=31-ge(n),t=r.hiddenUpdates,l=t[i],l===null?t[i]=[e]:l.push(e),e.lane=n|536870912),r):null}function lu(t){if(50<vr)throw vr=0,ns=null,Error(w(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var li={};function vv(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function he(t,e,n,l){return new vv(t,e,n,l)}function Ds(t){return t=t.prototype,!(!t||!t.isReactComponent)}function sn(t,e){var n=t.alternate;return n===null?(n=he(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Ud(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Va(t,e,n,l,i,r){var a=0;if(l=t,typeof t=="function")Ds(t)&&(a=1);else if(typeof t=="string")a=vS(t,n,Ve.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Ec:return t=he(31,n,e,i),t.elementType=Ec,t.lanes=r,t;case Il:return cl(n.children,i,r,e);case ed:a=8,i|=24;break;case vc:return t=he(12,n,e,i|2),t.elementType=vc,t.lanes=r,t;case Sc:return t=he(13,n,e,i),t.elementType=Sc,t.lanes=r,t;case kc:return t=he(19,n,e,i),t.elementType=kc,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case sb:case rn:a=10;break t;case nd:a=9;break t;case ds:a=11;break t;case gs:a=14;break t;case An:a=16,l=null;break t}a=29,n=Error(w(130,t===null?"null":typeof t,"")),l=null}return e=he(a,n,e,i),e.elementType=t,e.type=l,e.lanes=r,e}function cl(t,e,n,l){return t=he(7,t,l,e),t.lanes=n,t}function tc(t,e,n){return t=he(6,t,null,e),t.lanes=n,t}function ec(t,e,n){return e=he(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ii=[],ri=0,iu=null,ru=0,Ee=[],Te=0,sl=null,an=1,un="";function al(t,e){ii[ri++]=ru,ii[ri++]=iu,iu=t,ru=e}function Bd(t,e,n){Ee[Te++]=an,Ee[Te++]=un,Ee[Te++]=sl,sl=t;var l=an;t=un;var i=32-ge(l)-1;l&=~(1<<i),n+=1;var r=32-ge(e)+i;if(30<r){var a=i-i%5;r=(l&(1<<a)-1).toString(32),l>>=a,i-=a,an=1<<32-ge(e)+i|n<<i|l,un=r+t}else an=1<<r|n<<i|l,un=t}function Ms(t){t.return!==null&&(al(t,1),Bd(t,1,0))}function Os(t){for(;t===iu;)iu=ii[--ri],ii[ri]=null,ru=ii[--ri],ii[ri]=null;for(;t===sl;)sl=Ee[--Te],Ee[Te]=null,un=Ee[--Te],Ee[Te]=null,an=Ee[--Te],Ee[Te]=null}var ee=null,xt=null,$=!1,fl=null,je=!1,Uc=Error(w(519));function dl(t){var e=Error(w(418,""));throw wr(we(e,t)),Uc}function Pp(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[Kt]=t,e[ae]=l,n){case"dialog":F("cancel",e),F("close",e);break;case"iframe":case"object":case"embed":F("load",e);break;case"video":case"audio":for(n=0;n<Dr.length;n++)F(Dr[n],e);break;case"source":F("error",e);break;case"img":case"image":case"link":F("error",e),F("load",e);break;case"details":F("toggle",e);break;case"input":F("invalid",e),dd(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),eu(e);break;case"select":F("invalid",e);break;case"textarea":F("invalid",e),yd(e,l.value,l.defaultValue,l.children),eu(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||xy(e.textContent,n)?(l.popover!=null&&(F("beforetoggle",e),F("toggle",e)),l.onScroll!=null&&F("scroll",e),l.onScrollEnd!=null&&F("scrollend",e),l.onClick!=null&&(e.onclick=ju),e=!0):e=!1,e||dl(t)}function $p(t){for(ee=t.return;ee;)switch(ee.tag){case 5:case 13:je=!1;return;case 27:case 3:je=!0;return;default:ee=ee.return}}function Pi(t){if(t!==ee)return!1;if(!$)return $p(t),$=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||os(t.type,t.memoizedProps)),n=!n),n&&xt&&dl(t),$p(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(w(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){xt=Le(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}xt=null}}else e===27?(e=xt,Fn(t.type)?(t=fs,fs=null,xt=t):xt=e):xt=ee?Le(t.stateNode.nextSibling):null;return!0}function Vr(){xt=ee=null,$=!1}function th(){var t=fl;return t!==null&&(re===null?re=t:re.push.apply(re,t),fl=null),t}function wr(t){fl===null?fl=[t]:fl.push(t)}var Bc=Qe(null),kl=null,on=null;function zn(t,e,n){ht(Bc,e._currentValue),e._currentValue=n}function fn(t){t._currentValue=Bc.current,Ht(Bc)}function Hc(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function qc(t,e,n,l){var i=t.child;for(i!==null&&(i.return=t);i!==null;){var r=i.dependencies;if(r!==null){var a=i.child;r=r.firstContext;t:for(;r!==null;){var u=r;r=i;for(var o=0;o<e.length;o++)if(u.context===e[o]){r.lanes|=n,u=r.alternate,u!==null&&(u.lanes|=n),Hc(r.return,n,t),l||(a=null);break t}r=u.next}}else if(i.tag===18){if(a=i.return,a===null)throw Error(w(341));a.lanes|=n,r=a.alternate,r!==null&&(r.lanes|=n),Hc(a,n,t),a=null}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}}function Xr(t,e,n,l){t=null;for(var i=e,r=!1;i!==null;){if(!r){if(i.flags&524288)r=!0;else if(i.flags&262144)break}if(i.tag===10){var a=i.alternate;if(a===null)throw Error(w(387));if(a=a.memoizedProps,a!==null){var u=i.type;be(i.pendingProps.value,a.value)||(t!==null?t.push(u):t=[u])}}else if(i===Wa.current){if(a=i.alternate,a===null)throw Error(w(387));a.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(t!==null?t.push(_r):t=[_r])}i=i.return}t!==null&&qc(e,t,n,l),e.flags|=262144}function au(t){for(t=t.firstContext;t!==null;){if(!be(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function gl(t){kl=t,on=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function It(t){return Hd(kl,t)}function Ca(t,e){return kl===null&&gl(t),Hd(t,e)}function Hd(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},on===null){if(t===null)throw Error(w(308));on=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else on=on.next=e;return n}var Sv=typeof AbortController!="undefined"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},kv=_t.unstable_scheduleCallback,Ev=_t.unstable_NormalPriority,Mt={$$typeof:rn,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function _s(){return{controller:new Sv,data:new Map,refCount:0}}function Gr(t){t.refCount--,t.refCount===0&&kv(Ev,function(){t.controller.abort()})}var fr=null,jc=0,xi=0,si=null;function Tv(t,e){if(fr===null){var n=fr=[];jc=0,xi=ef(),si={status:"pending",value:void 0,then:function(l){n.push(l)}}}return jc++,e.then(eh,eh),e}function eh(){if(--jc===0&&fr!==null){si!==null&&(si.status="fulfilled");var t=fr;fr=null,xi=0,si=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Av(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var i=0;i<n.length;i++)(0,n[i])(e)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var nh=H.S;H.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Tv(t,e),nh!==null&&nh(t,e)};var ml=Qe(null);function Rs(){var t=ml.current;return t!==null?t:ct.pooledCache}function Xa(t,e){e===null?ht(ml,ml.current):ht(ml,e.pool)}function qd(){var t=Rs();return t===null?null:{parent:Mt._currentValue,pool:t}}var Qr=Error(w(460)),jd=Error(w(474)),Ru=Error(w(542)),Yc={then:function(){}};function lh(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Da(){}function Yd(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Da,Da),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,rh(t),t;default:if(typeof e.status=="string")e.then(Da,Da);else{if(t=ct,t!==null&&100<t.shellSuspendCounter)throw Error(w(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var i=e;i.status="fulfilled",i.value=l}},function(l){if(e.status==="pending"){var i=e;i.status="rejected",i.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,rh(t),t}throw mr=e,Qr}}var mr=null;function ih(){if(mr===null)throw Error(w(459));var t=mr;return mr=null,t}function rh(t){if(t===Qr||t===Ru)throw Error(w(483))}var wn=!1;function Ns(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Vc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Un(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Bn(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,nt&2){var i=l.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),l.pending=e,e=lu(t),Ld(t,null,n),e}return _u(t,l,e,n),lu(t)}function pr(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,cd(t,n)}}function nc(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var a={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?i=r=a:r=r.next=a,n=n.next}while(n!==null);r===null?i=r=e:r=r.next=e}else i=r=e;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Xc=!1;function hr(){if(Xc){var t=si;if(t!==null)throw t}}function dr(t,e,n,l){Xc=!1;var i=t.updateQueue;wn=!1;var r=i.firstBaseUpdate,a=i.lastBaseUpdate,u=i.shared.pending;if(u!==null){i.shared.pending=null;var o=u,c=o.next;o.next=null,a===null?r=c:a.next=c,a=o;var s=t.alternate;s!==null&&(s=s.updateQueue,u=s.lastBaseUpdate,u!==a&&(u===null?s.firstBaseUpdate=c:u.next=c,s.lastBaseUpdate=o))}if(r!==null){var f=i.baseState;a=0,s=c=o=null,u=r;do{var p=u.lane&-536870913,m=p!==u.lane;if(m?(W&p)===p:(l&p)===p){p!==0&&p===xi&&(Xc=!0),s!==null&&(s=s.next={lane:0,tag:u.tag,payload:u.payload,callback:null,next:null});t:{var y=t,v=u;p=e;var T=n;switch(v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(T,f,p);break t}f=y;break t;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,p=typeof y=="function"?y.call(T,f,p):y,p==null)break t;f=st({},f,p);break t;case 2:wn=!0}}p=u.callback,p!==null&&(t.flags|=64,m&&(t.flags|=8192),m=i.callbacks,m===null?i.callbacks=[p]:m.push(p))}else m={lane:p,tag:u.tag,payload:u.payload,callback:u.callback,next:null},s===null?(c=s=m,o=f):s=s.next=m,a|=p;if(u=u.next,u===null){if(u=i.shared.pending,u===null)break;m=u,u=m.next,m.next=null,i.lastBaseUpdate=m,i.shared.pending=null}}while(!0);s===null&&(o=f),i.baseState=o,i.firstBaseUpdate=c,i.lastBaseUpdate=s,r===null&&(i.shared.lanes=0),Qn|=a,t.lanes=a,t.memoizedState=f}}function Vd(t,e){if(typeof t!="function")throw Error(w(191,t));t.call(e)}function Xd(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Vd(n[t],e)}var bi=Qe(null),uu=Qe(0);function ah(t,e){t=dn,ht(uu,t),ht(bi,e),dn=t|e.baseLanes}function Gc(){ht(uu,dn),ht(bi,bi.current)}function Ls(){dn=uu.current,Ht(bi),Ht(uu)}var Xn=0,G=null,at=null,zt=null,ou=!1,fi=!1,yl=!1,cu=0,zr=0,mi=null,wv=0;function kt(){throw Error(w(321))}function Us(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!be(t[n],e[n]))return!1;return!0}function Bs(t,e,n,l,i,r){return Xn=r,G=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,H.H=t===null||t.memoizedState===null?vg:Sg,yl=!1,r=n(l,i),yl=!1,fi&&(r=Qd(e,n,l,i)),Gd(t),r}function Gd(t){H.H=su;var e=at!==null&&at.next!==null;if(Xn=0,zt=at=G=null,ou=!1,zr=0,mi=null,e)throw Error(w(300));t===null||Bt||(t=t.dependencies,t!==null&&au(t)&&(Bt=!0))}function Qd(t,e,n,l){G=t;var i=0;do{if(fi&&(mi=null),zr=0,fi=!1,25<=i)throw Error(w(301));if(i+=1,zt=at=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}H.H=Rv,r=e(n,l)}while(fi);return r}function zv(){var t=H.H,e=t.useState()[0];return e=typeof e.then=="function"?Zr(e):e,t=t.useState()[0],(at!==null?at.memoizedState:null)!==t&&(G.flags|=1024),e}function Hs(){var t=cu!==0;return cu=0,t}function qs(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function js(t){if(ou){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ou=!1}Xn=0,zt=at=G=null,fi=!1,zr=cu=0,mi=null}function le(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return zt===null?G.memoizedState=zt=t:zt=zt.next=t,zt}function Ct(){if(at===null){var t=G.alternate;t=t!==null?t.memoizedState:null}else t=at.next;var e=zt===null?G.memoizedState:zt.next;if(e!==null)zt=e,at=t;else{if(t===null)throw G.alternate===null?Error(w(467)):Error(w(310));at=t,t={memoizedState:at.memoizedState,baseState:at.baseState,baseQueue:at.baseQueue,queue:at.queue,next:null},zt===null?G.memoizedState=zt=t:zt=zt.next=t}return zt}function Ys(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Zr(t){var e=zr;return zr+=1,mi===null&&(mi=[]),t=Yd(mi,t,e),e=G,(zt===null?e.memoizedState:zt.next)===null&&(e=e.alternate,H.H=e===null||e.memoizedState===null?vg:Sg),t}function Nu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Zr(t);if(t.$$typeof===rn)return It(t)}throw Error(w(438,String(t)))}function Vs(t){var e=null,n=G.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=G.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(i){return i.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=Ys(),G.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=fb;return e.index++,n}function pn(t,e){return typeof e=="function"?e(t):e}function Ga(t){var e=Ct();return Xs(e,at,t)}function Xs(t,e,n){var l=t.queue;if(l===null)throw Error(w(311));l.lastRenderedReducer=n;var i=t.baseQueue,r=l.pending;if(r!==null){if(i!==null){var a=i.next;i.next=r.next,r.next=a}e.baseQueue=i=r,l.pending=null}if(r=t.baseState,i===null)t.memoizedState=r;else{e=i.next;var u=a=null,o=null,c=e,s=!1;do{var f=c.lane&-536870913;if(f!==c.lane?(W&f)===f:(Xn&f)===f){var p=c.revertLane;if(p===0)o!==null&&(o=o.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===xi&&(s=!0);else if((Xn&p)===p){c=c.next,p===xi&&(s=!0);continue}else f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},o===null?(u=o=f,a=r):o=o.next=f,G.lanes|=p,Qn|=p;f=c.action,yl&&n(r,f),r=c.hasEagerState?c.eagerState:n(r,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},o===null?(u=o=p,a=r):o=o.next=p,G.lanes|=f,Qn|=f;c=c.next}while(c!==null&&c!==e);if(o===null?a=r:o.next=u,!be(r,t.memoizedState)&&(Bt=!0,s&&(n=si,n!==null)))throw n;t.memoizedState=r,t.baseState=a,t.baseQueue=o,l.lastRenderedState=r}return i===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function lc(t){var e=Ct(),n=e.queue;if(n===null)throw Error(w(311));n.lastRenderedReducer=t;var l=n.dispatch,i=n.pending,r=e.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do r=t(r,a.action),a=a.next;while(a!==i);be(r,e.memoizedState)||(Bt=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,l]}function Zd(t,e,n){var l=G,i=Ct(),r=$;if(r){if(n===void 0)throw Error(w(407));n=n()}else n=e();var a=!be((at||i).memoizedState,n);a&&(i.memoizedState=n,Bt=!0),i=i.queue;var u=Id.bind(null,l,i,t);if(Fr(2048,8,u,[t]),i.getSnapshot!==e||a||zt!==null&&zt.memoizedState.tag&1){if(l.flags|=2048,vi(9,Lu(),Kd.bind(null,l,i,n,e),null),ct===null)throw Error(w(349));r||Xn&124||Fd(l,e,n)}return n}function Fd(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=G.updateQueue,e===null?(e=Ys(),G.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Kd(t,e,n,l){e.value=n,e.getSnapshot=l,Jd(e)&&Wd(t)}function Id(t,e,n){return n(function(){Jd(e)&&Wd(t)})}function Jd(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!be(t,n)}catch(l){return!0}}function Wd(t){var e=Ci(t,2);e!==null&&xe(e,t,2)}function Qc(t){var e=le();if(typeof t=="function"){var n=t;if(t=n(),yl){On(!0);try{n()}finally{On(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:t},e}function Pd(t,e,n,l){return t.baseState=n,Xs(t,at,typeof l=="function"?l:pn)}function Cv(t,e,n,l,i){if(Uu(t))throw Error(w(485));if(t=e.action,t!==null){var r={payload:i,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(a){r.listeners.push(a)}};H.T!==null?n(!0):r.isTransition=!1,l(r),n=e.pending,n===null?(r.next=e.pending=r,$d(e,r)):(r.next=n.next,e.pending=n.next=r)}}function $d(t,e){var n=e.action,l=e.payload,i=t.state;if(e.isTransition){var r=H.T,a={};H.T=a;try{var u=n(i,l),o=H.S;o!==null&&o(a,u),uh(t,e,u)}catch(c){Zc(t,e,c)}finally{H.T=r}}else try{r=n(i,l),uh(t,e,r)}catch(c){Zc(t,e,c)}}function uh(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){oh(t,e,l)},function(l){return Zc(t,e,l)}):oh(t,e,n)}function oh(t,e,n){e.status="fulfilled",e.value=n,tg(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,$d(t,n)))}function Zc(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,tg(e),e=e.next;while(e!==l)}t.action=null}function tg(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function eg(t,e){return e}function ch(t,e){if($){var n=ct.formState;if(n!==null){t:{var l=G;if($){if(xt){e:{for(var i=xt,r=je;i.nodeType!==8;){if(!r){i=null;break e}if(i=Le(i.nextSibling),i===null){i=null;break e}}r=i.data,i=r==="F!"||r==="F"?i:null}if(i){xt=Le(i.nextSibling),l=i.data==="F!";break t}}dl(l)}l=!1}l&&(e=n[0])}}return n=le(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:eg,lastRenderedState:e},n.queue=l,n=yg.bind(null,G,l),l.dispatch=n,l=Qc(!1),r=Fs.bind(null,G,!1,l.queue),l=le(),i={state:e,dispatch:null,action:t,pending:null},l.queue=i,n=Cv.bind(null,G,i,r,n),i.dispatch=n,l.memoizedState=t,[e,n,!1]}function sh(t){var e=Ct();return ng(e,at,t)}function ng(t,e,n){if(e=Xs(t,e,eg)[0],t=Ga(pn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=Zr(e)}catch(a){throw a===Qr?Ru:a}else l=e;e=Ct();var i=e.queue,r=i.dispatch;return n!==e.memoizedState&&(G.flags|=2048,vi(9,Lu(),Dv.bind(null,i,n),null)),[l,r,t]}function Dv(t,e){t.action=e}function fh(t){var e=Ct(),n=at;if(n!==null)return ng(e,n,t);Ct(),e=e.memoizedState,n=Ct();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function vi(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=G.updateQueue,e===null&&(e=Ys(),G.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Lu(){return{destroy:void 0,resource:void 0}}function lg(){return Ct().memoizedState}function Qa(t,e,n,l){var i=le();l=l===void 0?null:l,G.flags|=t,i.memoizedState=vi(1|e,Lu(),n,l)}function Fr(t,e,n,l){var i=Ct();l=l===void 0?null:l;var r=i.memoizedState.inst;at!==null&&l!==null&&Us(l,at.memoizedState.deps)?i.memoizedState=vi(e,r,n,l):(G.flags|=t,i.memoizedState=vi(1|e,r,n,l))}function mh(t,e){Qa(8390656,8,t,e)}function ig(t,e){Fr(2048,8,t,e)}function rg(t,e){return Fr(4,2,t,e)}function ag(t,e){return Fr(4,4,t,e)}function ug(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function og(t,e,n){n=n!=null?n.concat([t]):null,Fr(4,4,ug.bind(null,e,t),n)}function Gs(){}function cg(t,e){var n=Ct();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Us(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function sg(t,e){var n=Ct();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Us(e,l[1]))return l[0];if(l=t(),yl){On(!0);try{t()}finally{On(!1)}}return n.memoizedState=[l,e],l}function Qs(t,e,n){return n===void 0||Xn&1073741824?t.memoizedState=e:(t.memoizedState=n,t=ty(),G.lanes|=t,Qn|=t,n)}function fg(t,e,n,l){return be(n,e)?n:bi.current!==null?(t=Qs(t,n,l),be(t,e)||(Bt=!0),t):Xn&42?(t=ty(),G.lanes|=t,Qn|=t,e):(Bt=!0,t.memoizedState=n)}function mg(t,e,n,l,i){var r=tt.p;tt.p=r!==0&&8>r?r:8;var a=H.T,u={};H.T=u,Fs(t,!1,e,n);try{var o=i(),c=H.S;if(c!==null&&c(u,o),o!==null&&typeof o=="object"&&typeof o.then=="function"){var s=Av(o,l);gr(t,e,s,ye(t))}else gr(t,e,l,ye(t))}catch(f){gr(t,e,{then:function(){},status:"rejected",reason:f},ye())}finally{tt.p=r,H.T=a}}function Mv(){}function Fc(t,e,n,l){if(t.tag!==5)throw Error(w(476));var i=pg(t).queue;mg(t,i,e,ol,n===null?Mv:function(){return hg(t),n(l)})}function pg(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:ol,baseState:ol,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:ol},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function hg(t){var e=pg(t).next.queue;gr(t,e,{},ye())}function Zs(){return It(_r)}function dg(){return Ct().memoizedState}function gg(){return Ct().memoizedState}function Ov(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ye();t=Un(n);var l=Bn(e,t,n);l!==null&&(xe(l,e,n),pr(l,e,n)),e={cache:_s()},t.payload=e;return}e=e.return}}function _v(t,e,n){var l=ye();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Uu(t)?xg(e,n):(n=Cs(t,e,n,l),n!==null&&(xe(n,t,l),bg(n,e,l)))}function yg(t,e,n){var l=ye();gr(t,e,n,l)}function gr(t,e,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uu(t))xg(e,i);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var a=e.lastRenderedState,u=r(a,n);if(i.hasEagerState=!0,i.eagerState=u,be(u,a))return _u(t,e,i,0),ct===null&&Ou(),!1}catch(o){}finally{}if(n=Cs(t,e,i,l),n!==null)return xe(n,t,l),bg(n,e,l),!0}return!1}function Fs(t,e,n,l){if(l={lane:2,revertLane:ef(),action:l,hasEagerState:!1,eagerState:null,next:null},Uu(t)){if(e)throw Error(w(479))}else e=Cs(t,n,l,2),e!==null&&xe(e,t,2)}function Uu(t){var e=t.alternate;return t===G||e!==null&&e===G}function xg(t,e){fi=ou=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function bg(t,e,n){if(n&4194048){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,cd(t,n)}}var su={readContext:It,use:Nu,useCallback:kt,useContext:kt,useEffect:kt,useImperativeHandle:kt,useLayoutEffect:kt,useInsertionEffect:kt,useMemo:kt,useReducer:kt,useRef:kt,useState:kt,useDebugValue:kt,useDeferredValue:kt,useTransition:kt,useSyncExternalStore:kt,useId:kt,useHostTransitionStatus:kt,useFormState:kt,useActionState:kt,useOptimistic:kt,useMemoCache:kt,useCacheRefresh:kt},vg={readContext:It,use:Nu,useCallback:function(t,e){return le().memoizedState=[t,e===void 0?null:e],t},useContext:It,useEffect:mh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Qa(4194308,4,ug.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Qa(4194308,4,t,e)},useInsertionEffect:function(t,e){Qa(4,2,t,e)},useMemo:function(t,e){var n=le();e=e===void 0?null:e;var l=t();if(yl){On(!0);try{t()}finally{On(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=le();if(n!==void 0){var i=n(e);if(yl){On(!0);try{n(e)}finally{On(!1)}}}else i=e;return l.memoizedState=l.baseState=i,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:i},l.queue=t,t=t.dispatch=_v.bind(null,G,t),[l.memoizedState,t]},useRef:function(t){var e=le();return t={current:t},e.memoizedState=t},useState:function(t){t=Qc(t);var e=t.queue,n=yg.bind(null,G,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Gs,useDeferredValue:function(t,e){var n=le();return Qs(n,t,e)},useTransition:function(){var t=Qc(!1);return t=mg.bind(null,G,t.queue,!0,!1),le().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=G,i=le();if($){if(n===void 0)throw Error(w(407));n=n()}else{if(n=e(),ct===null)throw Error(w(349));W&124||Fd(l,e,n)}i.memoizedState=n;var r={value:n,getSnapshot:e};return i.queue=r,mh(Id.bind(null,l,r,t),[t]),l.flags|=2048,vi(9,Lu(),Kd.bind(null,l,r,n,e),null),n},useId:function(){var t=le(),e=ct.identifierPrefix;if($){var n=un,l=an;n=(l&~(1<<32-ge(l)-1)).toString(32)+n,e="\xAB"+e+"R"+n,n=cu++,0<n&&(e+="H"+n.toString(32)),e+="\xBB"}else n=wv++,e="\xAB"+e+"r"+n.toString(32)+"\xBB";return t.memoizedState=e},useHostTransitionStatus:Zs,useFormState:ch,useActionState:ch,useOptimistic:function(t){var e=le();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=Fs.bind(null,G,!0,n),n.dispatch=e,[t,e]},useMemoCache:Vs,useCacheRefresh:function(){return le().memoizedState=Ov.bind(null,G)}},Sg={readContext:It,use:Nu,useCallback:cg,useContext:It,useEffect:ig,useImperativeHandle:og,useInsertionEffect:rg,useLayoutEffect:ag,useMemo:sg,useReducer:Ga,useRef:lg,useState:function(){return Ga(pn)},useDebugValue:Gs,useDeferredValue:function(t,e){var n=Ct();return fg(n,at.memoizedState,t,e)},useTransition:function(){var t=Ga(pn)[0],e=Ct().memoizedState;return[typeof t=="boolean"?t:Zr(t),e]},useSyncExternalStore:Zd,useId:dg,useHostTransitionStatus:Zs,useFormState:sh,useActionState:sh,useOptimistic:function(t,e){var n=Ct();return Pd(n,at,t,e)},useMemoCache:Vs,useCacheRefresh:gg},Rv={readContext:It,use:Nu,useCallback:cg,useContext:It,useEffect:ig,useImperativeHandle:og,useInsertionEffect:rg,useLayoutEffect:ag,useMemo:sg,useReducer:lc,useRef:lg,useState:function(){return lc(pn)},useDebugValue:Gs,useDeferredValue:function(t,e){var n=Ct();return at===null?Qs(n,t,e):fg(n,at.memoizedState,t,e)},useTransition:function(){var t=lc(pn)[0],e=Ct().memoizedState;return[typeof t=="boolean"?t:Zr(t),e]},useSyncExternalStore:Zd,useId:dg,useHostTransitionStatus:Zs,useFormState:fh,useActionState:fh,useOptimistic:function(t,e){var n=Ct();return at!==null?Pd(n,at,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:Vs,useCacheRefresh:gg},pi=null,Cr=0;function Ma(t){var e=Cr;return Cr+=1,pi===null&&(pi=[]),Yd(pi,t,e)}function $i(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Oa(t,e){throw e.$$typeof===cb?Error(w(525)):(t=Object.prototype.toString.call(e),Error(w(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function ph(t){var e=t._init;return e(t._payload)}function kg(t){function e(h,d){if(t){var g=h.deletions;g===null?(h.deletions=[d],h.flags|=16):g.push(d)}}function n(h,d){if(!t)return null;for(;d!==null;)e(h,d),d=d.sibling;return null}function l(h){for(var d=new Map;h!==null;)h.key!==null?d.set(h.key,h):d.set(h.index,h),h=h.sibling;return d}function i(h,d){return h=sn(h,d),h.index=0,h.sibling=null,h}function r(h,d,g){return h.index=g,t?(g=h.alternate,g!==null?(g=g.index,g<d?(h.flags|=67108866,d):g):(h.flags|=67108866,d)):(h.flags|=1048576,d)}function a(h){return t&&h.alternate===null&&(h.flags|=67108866),h}function u(h,d,g,k){return d===null||d.tag!==6?(d=tc(g,h.mode,k),d.return=h,d):(d=i(d,g),d.return=h,d)}function o(h,d,g,k){var z=g.type;return z===Il?s(h,d,g.props.children,k,g.key):d!==null&&(d.elementType===z||typeof z=="object"&&z!==null&&z.$$typeof===An&&ph(z)===d.type)?(d=i(d,g.props),$i(d,g),d.return=h,d):(d=Va(g.type,g.key,g.props,null,h.mode,k),$i(d,g),d.return=h,d)}function c(h,d,g,k){return d===null||d.tag!==4||d.stateNode.containerInfo!==g.containerInfo||d.stateNode.implementation!==g.implementation?(d=ec(g,h.mode,k),d.return=h,d):(d=i(d,g.children||[]),d.return=h,d)}function s(h,d,g,k,z){return d===null||d.tag!==7?(d=cl(g,h.mode,k,z),d.return=h,d):(d=i(d,g),d.return=h,d)}function f(h,d,g){if(typeof d=="string"&&d!==""||typeof d=="number"||typeof d=="bigint")return d=tc(""+d,h.mode,g),d.return=h,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Sa:return g=Va(d.type,d.key,d.props,null,h.mode,g),$i(g,d),g.return=h,g;case ir:return d=ec(d,h.mode,g),d.return=h,d;case An:var k=d._init;return d=k(d._payload),f(h,d,g)}if(rr(d)||Ji(d))return d=cl(d,h.mode,g,null),d.return=h,d;if(typeof d.then=="function")return f(h,Ma(d),g);if(d.$$typeof===rn)return f(h,Ca(h,d),g);Oa(h,d)}return null}function p(h,d,g,k){var z=d!==null?d.key:null;if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return z!==null?null:u(h,d,""+g,k);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Sa:return g.key===z?o(h,d,g,k):null;case ir:return g.key===z?c(h,d,g,k):null;case An:return z=g._init,g=z(g._payload),p(h,d,g,k)}if(rr(g)||Ji(g))return z!==null?null:s(h,d,g,k,null);if(typeof g.then=="function")return p(h,d,Ma(g),k);if(g.$$typeof===rn)return p(h,d,Ca(h,g),k);Oa(h,g)}return null}function m(h,d,g,k,z){if(typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint")return h=h.get(g)||null,u(d,h,""+k,z);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case Sa:return h=h.get(k.key===null?g:k.key)||null,o(d,h,k,z);case ir:return h=h.get(k.key===null?g:k.key)||null,c(d,h,k,z);case An:var E=k._init;return k=E(k._payload),m(h,d,g,k,z)}if(rr(k)||Ji(k))return h=h.get(g)||null,s(d,h,k,z,null);if(typeof k.then=="function")return m(h,d,g,Ma(k),z);if(k.$$typeof===rn)return m(h,d,g,Ca(d,k),z);Oa(d,k)}return null}function y(h,d,g,k){for(var z=null,E=null,M=d,O=d=0,L=null;M!==null&&O<g.length;O++){M.index>O?(L=M,M=null):L=M.sibling;var S=p(h,M,g[O],k);if(S===null){M===null&&(M=L);break}t&&M&&S.alternate===null&&e(h,M),d=r(S,d,O),E===null?z=S:E.sibling=S,E=S,M=L}if(O===g.length)return n(h,M),$&&al(h,O),z;if(M===null){for(;O<g.length;O++)M=f(h,g[O],k),M!==null&&(d=r(M,d,O),E===null?z=M:E.sibling=M,E=M);return $&&al(h,O),z}for(M=l(M);O<g.length;O++)L=m(M,h,O,g[O],k),L!==null&&(t&&L.alternate!==null&&M.delete(L.key===null?O:L.key),d=r(L,d,O),E===null?z=L:E.sibling=L,E=L);return t&&M.forEach(function(J){return e(h,J)}),$&&al(h,O),z}function v(h,d,g,k){if(g==null)throw Error(w(151));for(var z=null,E=null,M=d,O=d=0,L=null,S=g.next();M!==null&&!S.done;O++,S=g.next()){M.index>O?(L=M,M=null):L=M.sibling;var J=p(h,M,S.value,k);if(J===null){M===null&&(M=L);break}t&&M&&J.alternate===null&&e(h,M),d=r(J,d,O),E===null?z=J:E.sibling=J,E=J,M=L}if(S.done)return n(h,M),$&&al(h,O),z;if(M===null){for(;!S.done;O++,S=g.next())S=f(h,S.value,k),S!==null&&(d=r(S,d,O),E===null?z=S:E.sibling=S,E=S);return $&&al(h,O),z}for(M=l(M);!S.done;O++,S=g.next())S=m(M,h,O,S.value,k),S!==null&&(t&&S.alternate!==null&&M.delete(S.key===null?O:S.key),d=r(S,d,O),E===null?z=S:E.sibling=S,E=S);return t&&M.forEach(function(Z){return e(h,Z)}),$&&al(h,O),z}function T(h,d,g,k){if(typeof g=="object"&&g!==null&&g.type===Il&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Sa:t:{for(var z=g.key;d!==null;){if(d.key===z){if(z=g.type,z===Il){if(d.tag===7){n(h,d.sibling),k=i(d,g.props.children),k.return=h,h=k;break t}}else if(d.elementType===z||typeof z=="object"&&z!==null&&z.$$typeof===An&&ph(z)===d.type){n(h,d.sibling),k=i(d,g.props),$i(k,g),k.return=h,h=k;break t}n(h,d);break}else e(h,d);d=d.sibling}g.type===Il?(k=cl(g.props.children,h.mode,k,g.key),k.return=h,h=k):(k=Va(g.type,g.key,g.props,null,h.mode,k),$i(k,g),k.return=h,h=k)}return a(h);case ir:t:{for(z=g.key;d!==null;){if(d.key===z)if(d.tag===4&&d.stateNode.containerInfo===g.containerInfo&&d.stateNode.implementation===g.implementation){n(h,d.sibling),k=i(d,g.children||[]),k.return=h,h=k;break t}else{n(h,d);break}else e(h,d);d=d.sibling}k=ec(g,h.mode,k),k.return=h,h=k}return a(h);case An:return z=g._init,g=z(g._payload),T(h,d,g,k)}if(rr(g))return y(h,d,g,k);if(Ji(g)){if(z=Ji(g),typeof z!="function")throw Error(w(150));return g=z.call(g),v(h,d,g,k)}if(typeof g.then=="function")return T(h,d,Ma(g),k);if(g.$$typeof===rn)return T(h,d,Ca(h,g),k);Oa(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint"?(g=""+g,d!==null&&d.tag===6?(n(h,d.sibling),k=i(d,g),k.return=h,h=k):(n(h,d),k=tc(g,h.mode,k),k.return=h,h=k),a(h)):n(h,d)}return function(h,d,g,k){try{Cr=0;var z=T(h,d,g,k);return pi=null,z}catch(M){if(M===Qr||M===Ru)throw M;var E=he(29,M,null,h.mode);return E.lanes=k,E.return=h,E}finally{}}}var Si=kg(!0),Eg=kg(!1),Ce=Qe(null),Ge=null;function Cn(t){var e=t.alternate;ht(Ot,Ot.current&1),ht(Ce,t),Ge===null&&(e===null||bi.current!==null||e.memoizedState!==null)&&(Ge=t)}function Tg(t){if(t.tag===22){if(ht(Ot,Ot.current),ht(Ce,t),Ge===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ge=t)}}else Dn(t)}function Dn(){ht(Ot,Ot.current),ht(Ce,Ce.current)}function cn(t){Ht(Ce),Ge===t&&(Ge=null),Ht(Ot)}var Ot=Qe(0);function fu(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||ss(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function ic(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:st({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Kc={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=ye(),i=Un(l);i.payload=e,n!=null&&(i.callback=n),e=Bn(t,i,l),e!==null&&(xe(e,t,l),pr(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=ye(),i=Un(l);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=Bn(t,i,l),e!==null&&(xe(e,t,l),pr(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ye(),l=Un(n);l.tag=2,e!=null&&(l.callback=e),e=Bn(t,l,n),e!==null&&(xe(e,t,n),pr(e,t,n))}};function hh(t,e,n,l,i,r,a){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,r,a):e.prototype&&e.prototype.isPureReactComponent?!Ar(n,l)||!Ar(i,r):!0}function dh(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&Kc.enqueueReplaceState(e,e.state,null)}function xl(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=st({},n));for(var i in t)n[i]===void 0&&(n[i]=t[i])}return n}var mu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Ag(t){mu(t)}function wg(t){console.error(t)}function zg(t){mu(t)}function pu(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function gh(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function Ic(t,e,n){return n=Un(n),n.tag=3,n.payload={element:null},n.callback=function(){pu(t,e)},n}function Cg(t){return t=Un(t),t.tag=3,t}function Dg(t,e,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var r=l.value;t.payload=function(){return i(r)},t.callback=function(){gh(e,n,l)}}var a=n.stateNode;a!==null&&typeof a.componentDidCatch=="function"&&(t.callback=function(){gh(e,n,l),typeof i!="function"&&(Hn===null?Hn=new Set([this]):Hn.add(this));var u=l.stack;this.componentDidCatch(l.value,{componentStack:u!==null?u:""})})}function Nv(t,e,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&Xr(e,n,i,!0),n=Ce.current,n!==null){switch(n.tag){case 13:return Ge===null?ls():n.alternate===null&&bt===0&&(bt=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Yc?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),dc(t,l,i)),!1;case 22:return n.flags|=65536,l===Yc?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),dc(t,l,i)),!1}throw Error(w(435,n.tag))}return dc(t,l,i),ls(),!1}if($)return e=Ce.current,e!==null?(!(e.flags&65536)&&(e.flags|=256),e.flags|=65536,e.lanes=i,l!==Uc&&(t=Error(w(422),{cause:l}),wr(we(t,n)))):(l!==Uc&&(e=Error(w(423),{cause:l}),wr(we(e,n))),t=t.current.alternate,t.flags|=65536,i&=-i,t.lanes|=i,l=we(l,n),i=Ic(t.stateNode,l,i),nc(t,i),bt!==4&&(bt=2)),!1;var r=Error(w(520),{cause:l});if(r=we(r,n),br===null?br=[r]:br.push(r),bt!==4&&(bt=2),e===null)return!0;l=we(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=i&-i,n.lanes|=t,t=Ic(n.stateNode,l,t),nc(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Hn===null||!Hn.has(r))))return n.flags|=65536,i&=-i,n.lanes|=i,i=Cg(i),Dg(i,t,n,l),nc(n,i),!1}n=n.return}while(n!==null);return!1}var Mg=Error(w(461)),Bt=!1;function Xt(t,e,n,l){e.child=t===null?Eg(e,null,n,l):Si(e,t.child,n,l)}function yh(t,e,n,l,i){n=n.render;var r=e.ref;if("ref"in l){var a={};for(var u in l)u!=="ref"&&(a[u]=l[u])}else a=l;return gl(e),l=Bs(t,e,n,a,r,i),u=Hs(),t!==null&&!Bt?(qs(t,e,i),hn(t,e,i)):($&&u&&Ms(e),e.flags|=1,Xt(t,e,l,i),e.child)}function xh(t,e,n,l,i){if(t===null){var r=n.type;return typeof r=="function"&&!Ds(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,Og(t,e,r,l,i)):(t=Va(n.type,null,l,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!Ks(t,i)){var a=r.memoizedProps;if(n=n.compare,n=n!==null?n:Ar,n(a,l)&&t.ref===e.ref)return hn(t,e,i)}return e.flags|=1,t=sn(r,l),t.ref=e.ref,t.return=e,e.child=t}function Og(t,e,n,l,i){if(t!==null){var r=t.memoizedProps;if(Ar(r,l)&&t.ref===e.ref)if(Bt=!1,e.pendingProps=l=r,Ks(t,i))t.flags&131072&&(Bt=!0);else return e.lanes=t.lanes,hn(t,e,i)}return Jc(t,e,n,l,i)}function _g(t,e,n){var l=e.pendingProps,i=l.children,r=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if(e.flags&128){if(l=r!==null?r.baseLanes|n:n,t!==null){for(i=e.child=t.child,r=0;i!==null;)r=r|i.lanes|i.childLanes,i=i.sibling;e.childLanes=r&~l}else e.childLanes=0,e.child=null;return bh(t,e,l,n)}if(n&536870912)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Xa(e,r!==null?r.cachePool:null),r!==null?ah(e,r):Gc(),Tg(e);else return e.lanes=e.childLanes=536870912,bh(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(Xa(e,r.cachePool),ah(e,r),Dn(e),e.memoizedState=null):(t!==null&&Xa(e,null),Gc(),Dn(e));return Xt(t,e,i,n),e.child}function bh(t,e,n,l){var i=Rs();return i=i===null?null:{parent:Mt._currentValue,pool:i},e.memoizedState={baseLanes:n,cachePool:i},t!==null&&Xa(e,null),Gc(),Tg(e),t!==null&&Xr(t,e,l,!0),null}function Za(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(w(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Jc(t,e,n,l,i){return gl(e),n=Bs(t,e,n,l,void 0,i),l=Hs(),t!==null&&!Bt?(qs(t,e,i),hn(t,e,i)):($&&l&&Ms(e),e.flags|=1,Xt(t,e,n,i),e.child)}function vh(t,e,n,l,i,r){return gl(e),e.updateQueue=null,n=Qd(e,l,n,i),Gd(t),l=Hs(),t!==null&&!Bt?(qs(t,e,r),hn(t,e,r)):($&&l&&Ms(e),e.flags|=1,Xt(t,e,n,r),e.child)}function Sh(t,e,n,l,i){if(gl(e),e.stateNode===null){var r=li,a=n.contextType;typeof a=="object"&&a!==null&&(r=It(a)),r=new n(l,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=Kc,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=l,r.state=e.memoizedState,r.refs={},Ns(e),a=n.contextType,r.context=typeof a=="object"&&a!==null?It(a):li,r.state=e.memoizedState,a=n.getDerivedStateFromProps,typeof a=="function"&&(ic(e,n,a,l),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(a=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),a!==r.state&&Kc.enqueueReplaceState(r,r.state,null),dr(e,l,r,i),hr(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){r=e.stateNode;var u=e.memoizedProps,o=xl(n,u);r.props=o;var c=r.context,s=n.contextType;a=li,typeof s=="object"&&s!==null&&(a=It(s));var f=n.getDerivedStateFromProps;s=typeof f=="function"||typeof r.getSnapshotBeforeUpdate=="function",u=e.pendingProps!==u,s||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(u||c!==a)&&dh(e,r,l,a),wn=!1;var p=e.memoizedState;r.state=p,dr(e,l,r,i),hr(),c=e.memoizedState,u||p!==c||wn?(typeof f=="function"&&(ic(e,n,f,l),c=e.memoizedState),(o=wn||hh(e,n,o,l,p,c,a))?(s||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=c),r.props=l,r.state=c,r.context=a,l=o):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{r=e.stateNode,Vc(t,e),a=e.memoizedProps,s=xl(n,a),r.props=s,f=e.pendingProps,p=r.context,c=n.contextType,o=li,typeof c=="object"&&c!==null&&(o=It(c)),u=n.getDerivedStateFromProps,(c=typeof u=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(a!==f||p!==o)&&dh(e,r,l,o),wn=!1,p=e.memoizedState,r.state=p,dr(e,l,r,i),hr();var m=e.memoizedState;a!==f||p!==m||wn||t!==null&&t.dependencies!==null&&au(t.dependencies)?(typeof u=="function"&&(ic(e,n,u,l),m=e.memoizedState),(s=wn||hh(e,n,s,l,p,m,o)||t!==null&&t.dependencies!==null&&au(t.dependencies))?(c||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,m,o),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,m,o)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=m),r.props=l,r.state=m,r.context=o,l=s):(typeof r.componentDidUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),l=!1)}return r=l,Za(t,e),l=(e.flags&128)!==0,r||l?(r=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&l?(e.child=Si(e,t.child,null,i),e.child=Si(e,null,n,i)):Xt(t,e,n,i),e.memoizedState=r.state,t=e.child):t=hn(t,e,i),t}function kh(t,e,n,l){return Vr(),e.flags|=256,Xt(t,e,n,l),e.child}var rc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ac(t){return{baseLanes:t,cachePool:qd()}}function uc(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=ze),t}function Rg(t,e,n){var l=e.pendingProps,i=!1,r=(e.flags&128)!==0,a;if((a=r)||(a=t!==null&&t.memoizedState===null?!1:(Ot.current&2)!==0),a&&(i=!0,e.flags&=-129),a=(e.flags&32)!==0,e.flags&=-33,t===null){if($){if(i?Cn(e):Dn(e),$){var u=xt,o;if(o=u){t:{for(o=u,u=je;o.nodeType!==8;){if(!u){u=null;break t}if(o=Le(o.nextSibling),o===null){u=null;break t}}u=o}u!==null?(e.memoizedState={dehydrated:u,treeContext:sl!==null?{id:an,overflow:un}:null,retryLane:536870912,hydrationErrors:null},o=he(18,null,null,0),o.stateNode=u,o.return=e,e.child=o,ee=e,xt=null,o=!0):o=!1}o||dl(e)}if(u=e.memoizedState,u!==null&&(u=u.dehydrated,u!==null))return ss(u)?e.lanes=32:e.lanes=536870912,null;cn(e)}return u=l.children,l=l.fallback,i?(Dn(e),i=e.mode,u=hu({mode:"hidden",children:u},i),l=cl(l,i,n,null),u.return=e,l.return=e,u.sibling=l,e.child=u,i=e.child,i.memoizedState=ac(n),i.childLanes=uc(t,a,n),e.memoizedState=rc,l):(Cn(e),Wc(e,u))}if(o=t.memoizedState,o!==null&&(u=o.dehydrated,u!==null)){if(r)e.flags&256?(Cn(e),e.flags&=-257,e=oc(t,e,n)):e.memoizedState!==null?(Dn(e),e.child=t.child,e.flags|=128,e=null):(Dn(e),i=l.fallback,u=e.mode,l=hu({mode:"visible",children:l.children},u),i=cl(i,u,n,null),i.flags|=2,l.return=e,i.return=e,l.sibling=i,e.child=l,Si(e,t.child,null,n),l=e.child,l.memoizedState=ac(n),l.childLanes=uc(t,a,n),e.memoizedState=rc,e=i);else if(Cn(e),ss(u)){if(a=u.nextSibling&&u.nextSibling.dataset,a)var c=a.dgst;a=c,l=Error(w(419)),l.stack="",l.digest=a,wr({value:l,source:null,stack:null}),e=oc(t,e,n)}else if(Bt||Xr(t,e,n,!1),a=(n&t.childLanes)!==0,Bt||a){if(a=ct,a!==null&&(l=n&-n,l=l&42?1:xs(l),l=l&(a.suspendedLanes|n)?0:l,l!==0&&l!==o.retryLane))throw o.retryLane=l,Ci(t,l),xe(a,t,l),Mg;u.data==="$?"||ls(),e=oc(t,e,n)}else u.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=o.treeContext,xt=Le(u.nextSibling),ee=e,$=!0,fl=null,je=!1,t!==null&&(Ee[Te++]=an,Ee[Te++]=un,Ee[Te++]=sl,an=t.id,un=t.overflow,sl=e),e=Wc(e,l.children),e.flags|=4096);return e}return i?(Dn(e),i=l.fallback,u=e.mode,o=t.child,c=o.sibling,l=sn(o,{mode:"hidden",children:l.children}),l.subtreeFlags=o.subtreeFlags&65011712,c!==null?i=sn(c,i):(i=cl(i,u,n,null),i.flags|=2),i.return=e,l.return=e,l.sibling=i,e.child=l,l=i,i=e.child,u=t.child.memoizedState,u===null?u=ac(n):(o=u.cachePool,o!==null?(c=Mt._currentValue,o=o.parent!==c?{parent:c,pool:c}:o):o=qd(),u={baseLanes:u.baseLanes|n,cachePool:o}),i.memoizedState=u,i.childLanes=uc(t,a,n),e.memoizedState=rc,l):(Cn(e),n=t.child,t=n.sibling,n=sn(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(a=e.deletions,a===null?(e.deletions=[t],e.flags|=16):a.push(t)),e.child=n,e.memoizedState=null,n)}function Wc(t,e){return e=hu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function hu(t,e){return t=he(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function oc(t,e,n){return Si(e,t.child,null,n),t=Wc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Eh(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Hc(t.return,e,n)}function cc(t,e,n,l,i){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=n,r.tailMode=i)}function Ng(t,e,n){var l=e.pendingProps,i=l.revealOrder,r=l.tail;if(Xt(t,e,l.children,n),l=Ot.current,l&2)l=l&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Eh(t,n,e);else if(t.tag===19)Eh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(ht(Ot,l),i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&fu(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),cc(e,!1,i,n,r);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&fu(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}cc(e,!0,n,null,r);break;case"together":cc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function hn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Qn|=e.lanes,!(n&e.childLanes))if(t!==null){if(Xr(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(w(153));if(e.child!==null){for(t=e.child,n=sn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=sn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Ks(t,e){return t.lanes&e?!0:(t=t.dependencies,!!(t!==null&&au(t)))}function Lv(t,e,n){switch(e.tag){case 3:Pa(e,e.stateNode.containerInfo),zn(e,Mt,t.memoizedState.cache),Vr();break;case 27:case 5:wc(e);break;case 4:Pa(e,e.stateNode.containerInfo);break;case 10:zn(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Cn(e),e.flags|=128,null):n&e.child.childLanes?Rg(t,e,n):(Cn(e),t=hn(t,e,n),t!==null?t.sibling:null);Cn(e);break;case 19:var i=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(Xr(t,e,n,!1),l=(n&e.childLanes)!==0),i){if(l)return Ng(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),ht(Ot,Ot.current),l)break;return null;case 22:case 23:return e.lanes=0,_g(t,e,n);case 24:zn(e,Mt,t.memoizedState.cache)}return hn(t,e,n)}function Lg(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Bt=!0;else{if(!Ks(t,n)&&!(e.flags&128))return Bt=!1,Lv(t,e,n);Bt=!!(t.flags&131072)}else Bt=!1,$&&e.flags&1048576&&Bd(e,ru,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,i=l._init;if(l=i(l._payload),e.type=l,typeof l=="function")Ds(l)?(t=xl(l,t),e.tag=1,e=Sh(null,e,l,t,n)):(e.tag=0,e=Jc(null,e,l,t,n));else{if(l!=null){if(i=l.$$typeof,i===ds){e.tag=11,e=yh(null,e,l,t,n);break t}else if(i===gs){e.tag=14,e=xh(null,e,l,t,n);break t}}throw e=Tc(l)||l,Error(w(306,e,""))}}return e;case 0:return Jc(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,i=xl(l,e.pendingProps),Sh(t,e,l,i,n);case 3:t:{if(Pa(e,e.stateNode.containerInfo),t===null)throw Error(w(387));l=e.pendingProps;var r=e.memoizedState;i=r.element,Vc(t,e),dr(e,l,null,n);var a=e.memoizedState;if(l=a.cache,zn(e,Mt,l),l!==r.cache&&qc(e,[Mt],n,!0),hr(),l=a.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:a.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=kh(t,e,l,n);break t}else if(l!==i){i=we(Error(w(424)),e),wr(i),e=kh(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(xt=Le(t.firstChild),ee=e,$=!0,fl=null,je=!0,n=Eg(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Vr(),l===i){e=hn(t,e,n);break t}Xt(t,e,l,n)}e=e.child}return e;case 26:return Za(t,e),t===null?(n=Vh(e.type,null,e.pendingProps,null))?e.memoizedState=n:$||(n=e.type,t=e.pendingProps,l=Su(Ln.current).createElement(n),l[Kt]=e,l[ae]=t,Qt(l,n,t),Ut(l),e.stateNode=l):e.memoizedState=Vh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return wc(e),t===null&&$&&(l=e.stateNode=Sy(e.type,e.pendingProps,Ln.current),ee=e,je=!0,i=xt,Fn(e.type)?(fs=i,xt=Le(l.firstChild)):xt=i),Xt(t,e,e.pendingProps.children,n),Za(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&$&&((i=l=xt)&&(l=uS(l,e.type,e.pendingProps,je),l!==null?(e.stateNode=l,ee=e,xt=Le(l.firstChild),je=!1,i=!0):i=!1),i||dl(e)),wc(e),i=e.type,r=e.pendingProps,a=t!==null?t.memoizedProps:null,l=r.children,os(i,r)?l=null:a!==null&&os(i,a)&&(e.flags|=32),e.memoizedState!==null&&(i=Bs(t,e,zv,null,null,n),_r._currentValue=i),Za(t,e),Xt(t,e,l,n),e.child;case 6:return t===null&&$&&((t=n=xt)&&(n=oS(n,e.pendingProps,je),n!==null?(e.stateNode=n,ee=e,xt=null,t=!0):t=!1),t||dl(e)),null;case 13:return Rg(t,e,n);case 4:return Pa(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Si(e,null,l,n):Xt(t,e,l,n),e.child;case 11:return yh(t,e,e.type,e.pendingProps,n);case 7:return Xt(t,e,e.pendingProps,n),e.child;case 8:return Xt(t,e,e.pendingProps.children,n),e.child;case 12:return Xt(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,zn(e,e.type,l.value),Xt(t,e,l.children,n),e.child;case 9:return i=e.type._context,l=e.pendingProps.children,gl(e),i=It(i),l=l(i),e.flags|=1,Xt(t,e,l,n),e.child;case 14:return xh(t,e,e.type,e.pendingProps,n);case 15:return Og(t,e,e.type,e.pendingProps,n);case 19:return Ng(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=hu(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=sn(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return _g(t,e,n);case 24:return gl(e),l=It(Mt),t===null?(i=Rs(),i===null&&(i=ct,r=_s(),i.pooledCache=r,r.refCount++,r!==null&&(i.pooledCacheLanes|=n),i=r),e.memoizedState={parent:l,cache:i},Ns(e),zn(e,Mt,i)):(t.lanes&n&&(Vc(t,e),dr(e,null,null,n),hr()),i=t.memoizedState,r=e.memoizedState,i.parent!==l?(i={parent:l,cache:l},e.memoizedState=i,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=i),zn(e,Mt,l)):(l=r.cache,zn(e,Mt,l),l!==i.cache&&qc(e,[Mt],n,!0))),Xt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(w(156,e.tag))}function en(t){t.flags|=4}function Th(t,e){if(e.type!=="stylesheet"||e.state.loading&4)t.flags&=-16777217;else if(t.flags|=16777216,!Ty(e)){if(e=Ce.current,e!==null&&((W&4194048)===W?Ge!==null:(W&62914560)!==W&&!(W&536870912)||e!==Ge))throw mr=Yc,jd;t.flags|=8192}}function _a(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?ud():536870912,t.lanes|=e,ki|=e)}function tr(t,e){if(!$)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function yt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function Uv(t,e,n){var l=e.pendingProps;switch(Os(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return yt(e),null;case 1:return yt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),fn(Mt),di(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Pi(e)?en(e):t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,th())),yt(e),null;case 26:return n=e.memoizedState,t===null?(en(e),n!==null?(yt(e),Th(e,n)):(yt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(en(e),yt(e),Th(e,n)):(yt(e),e.flags&=-16777217):(t.memoizedProps!==l&&en(e),yt(e),e.flags&=-16777217),null;case 27:$a(e),n=Ln.current;var i=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&en(e);else{if(!l){if(e.stateNode===null)throw Error(w(166));return yt(e),null}t=Ve.current,Pi(e)?Pp(e,t):(t=Sy(i,l,n),e.stateNode=t,en(e))}return yt(e),null;case 5:if($a(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&en(e);else{if(!l){if(e.stateNode===null)throw Error(w(166));return yt(e),null}if(t=Ve.current,Pi(e))Pp(e,t);else{switch(i=Su(Ln.current),t){case 1:t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=i.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}t[Kt]=e,t[ae]=l;t:for(i=e.child;i!==null;){if(i.tag===5||i.tag===6)t.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===e)break t;for(;i.sibling===null;){if(i.return===null||i.return===e)break t;i=i.return}i.sibling.return=i.return,i=i.sibling}e.stateNode=t;t:switch(Qt(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&en(e)}}return yt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&en(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(w(166));if(t=Ln.current,Pi(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,i=ee,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}t[Kt]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||xy(t.nodeValue,n)),t||dl(e)}else t=Su(t).createTextNode(l),t[Kt]=e,e.stateNode=t}return yt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(i=Pi(e),l!==null&&l.dehydrated!==null){if(t===null){if(!i)throw Error(w(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(w(317));i[Kt]=e}else Vr(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;yt(e),i=!1}else i=th(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=i),i=!0;if(!i)return e.flags&256?(cn(e),e):(cn(e),null)}if(cn(e),e.flags&128)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==i&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),_a(e,e.updateQueue),yt(e),null;case 4:return di(),t===null&&nf(e.stateNode.containerInfo),yt(e),null;case 10:return fn(e.type),yt(e),null;case 19:if(Ht(Ot),i=e.memoizedState,i===null)return yt(e),null;if(l=(e.flags&128)!==0,r=i.rendering,r===null)if(l)tr(i,!1);else{if(bt!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(r=fu(t),r!==null){for(e.flags|=128,tr(i,!1),t=r.updateQueue,e.updateQueue=t,_a(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Ud(n,t),n=n.sibling;return ht(Ot,Ot.current&1|2),e.child}t=t.sibling}i.tail!==null&&Xe()>gu&&(e.flags|=128,l=!0,tr(i,!1),e.lanes=4194304)}else{if(!l)if(t=fu(r),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,_a(e,t),tr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!r.alternate&&!$)return yt(e),null}else 2*Xe()-i.renderingStartTime>gu&&n!==536870912&&(e.flags|=128,l=!0,tr(i,!1),e.lanes=4194304);i.isBackwards?(r.sibling=e.child,e.child=r):(t=i.last,t!==null?t.sibling=r:e.child=r,i.last=r)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=Xe(),e.sibling=null,t=Ot.current,ht(Ot,l?t&1|2:t&1),e):(yt(e),null);case 22:case 23:return cn(e),Ls(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?n&536870912&&!(e.flags&128)&&(yt(e),e.subtreeFlags&6&&(e.flags|=8192)):yt(e),n=e.updateQueue,n!==null&&_a(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&Ht(ml),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),fn(Mt),yt(e),null;case 25:return null;case 30:return null}throw Error(w(156,e.tag))}function Bv(t,e){switch(Os(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return fn(Mt),di(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return $a(e),null;case 13:if(cn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(w(340));Vr()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Ht(Ot),null;case 4:return di(),null;case 10:return fn(e.type),null;case 22:case 23:return cn(e),Ls(),t!==null&&Ht(ml),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return fn(Mt),null;case 25:return null;default:return null}}function Ug(t,e){switch(Os(e),e.tag){case 3:fn(Mt),di();break;case 26:case 27:case 5:$a(e);break;case 4:di();break;case 13:cn(e);break;case 19:Ht(Ot);break;case 10:fn(e.type);break;case 22:case 23:cn(e),Ls(),t!==null&&Ht(ml);break;case 24:fn(Mt)}}function Kr(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&t)===t){l=void 0;var r=n.create,a=n.inst;l=r(),a.destroy=l}n=n.next}while(n!==i)}}catch(u){ot(e,e.return,u)}}function Gn(t,e,n){try{var l=e.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var r=i.next;l=r;do{if((l.tag&t)===t){var a=l.inst,u=a.destroy;if(u!==void 0){a.destroy=void 0,i=e;var o=n,c=u;try{c()}catch(s){ot(i,o,s)}}}l=l.next}while(l!==r)}}catch(s){ot(e,e.return,s)}}function Bg(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Xd(e,n)}catch(l){ot(t,t.return,l)}}}function Hg(t,e,n){n.props=xl(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){ot(t,e,l)}}function yr(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(i){ot(t,e,i)}}function Ye(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){ot(t,e,i)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){ot(t,e,i)}else n.current=null}function qg(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){ot(t,t.return,i)}}function sc(t,e,n){try{var l=t.stateNode;nS(l,t.type,n,e),l[ae]=e}catch(i){ot(t,t.return,i)}}function jg(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Fn(t.type)||t.tag===4}function fc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||jg(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Fn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Pc(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=ju));else if(l!==4&&(l===27&&Fn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(Pc(t,e,n),t=t.sibling;t!==null;)Pc(t,e,n),t=t.sibling}function du(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&Fn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(du(t,e,n),t=t.sibling;t!==null;)du(t,e,n),t=t.sibling}function Yg(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,i=e.attributes;i.length;)e.removeAttributeNode(i[0]);Qt(e,l,n),e[Kt]=t,e[ae]=n}catch(r){ot(t,t.return,r)}}var ln=!1,Et=!1,mc=!1,Ah=typeof WeakSet=="function"?WeakSet:Set,Lt=null;function Hv(t,e){if(t=t.containerInfo,as=Au,t=Cd(t),ws(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{n.nodeType,r.nodeType}catch(v){n=null;break t}var a=0,u=-1,o=-1,c=0,s=0,f=t,p=null;e:for(;;){for(var m;f!==n||i!==0&&f.nodeType!==3||(u=a+i),f!==r||l!==0&&f.nodeType!==3||(o=a+l),f.nodeType===3&&(a+=f.nodeValue.length),(m=f.firstChild)!==null;)p=f,f=m;for(;;){if(f===t)break e;if(p===n&&++c===i&&(u=a),p===r&&++s===l&&(o=a),(m=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=m}n=u===-1||o===-1?null:{start:u,end:o}}else n=null}n=n||{start:0,end:0}}else n=null;for(us={focusedElem:t,selectionRange:n},Au=!1,Lt=e;Lt!==null;)if(e=Lt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Lt=t;else for(;Lt!==null;){switch(e=Lt,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if(t&1024&&r!==null){t=void 0,n=e,i=r.memoizedProps,r=r.memoizedState,l=n.stateNode;try{var y=xl(n.type,i,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(y,r),l.__reactInternalSnapshotBeforeUpdate=t}catch(v){ot(n,n.return,v)}}break;case 3:if(t&1024){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)cs(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":cs(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(t&1024)throw Error(w(163))}if(t=e.sibling,t!==null){t.return=e.return,Lt=t;break}Lt=e.return}}function Vg(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:En(t,n),l&4&&Kr(5,n);break;case 1:if(En(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(a){ot(n,n.return,a)}else{var i=xl(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(i,e,t.__reactInternalSnapshotBeforeUpdate)}catch(a){ot(n,n.return,a)}}l&64&&Bg(n),l&512&&yr(n,n.return);break;case 3:if(En(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Xd(t,e)}catch(a){ot(n,n.return,a)}}break;case 27:e===null&&l&4&&Yg(n);case 26:case 5:En(t,n),e===null&&l&4&&qg(n),l&512&&yr(n,n.return);break;case 12:En(t,n);break;case 13:En(t,n),l&4&&Qg(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=Fv.bind(null,n),cS(t,n))));break;case 22:if(l=n.memoizedState!==null||ln,!l){e=e!==null&&e.memoizedState!==null||Et,i=ln;var r=Et;ln=l,(Et=e)&&!r?Tn(t,n,(n.subtreeFlags&8772)!==0):En(t,n),ln=i,Et=r}break;case 30:break;default:En(t,n)}}function Xg(t){var e=t.alternate;e!==null&&(t.alternate=null,Xg(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&vs(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var pt=null,ie=!1;function nn(t,e,n){for(n=n.child;n!==null;)Gg(t,e,n),n=n.sibling}function Gg(t,e,n){if(de&&typeof de.onCommitFiberUnmount=="function")try{de.onCommitFiberUnmount(Br,n)}catch(r){}switch(n.tag){case 26:Et||Ye(n,e),nn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Et||Ye(n,e);var l=pt,i=ie;Fn(n.type)&&(pt=n.stateNode,ie=!1),nn(t,e,n),Sr(n.stateNode),pt=l,ie=i;break;case 5:Et||Ye(n,e);case 6:if(l=pt,i=ie,pt=null,nn(t,e,n),pt=l,ie=i,pt!==null)if(ie)try{(pt.nodeType===9?pt.body:pt.nodeName==="HTML"?pt.ownerDocument.body:pt).removeChild(n.stateNode)}catch(r){ot(n,e,r)}else try{pt.removeChild(n.stateNode)}catch(r){ot(n,e,r)}break;case 18:pt!==null&&(ie?(t=pt,qh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),Lr(t)):qh(pt,n.stateNode));break;case 4:l=pt,i=ie,pt=n.stateNode.containerInfo,ie=!0,nn(t,e,n),pt=l,ie=i;break;case 0:case 11:case 14:case 15:Et||Gn(2,n,e),Et||Gn(4,n,e),nn(t,e,n);break;case 1:Et||(Ye(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Hg(n,e,l)),nn(t,e,n);break;case 21:nn(t,e,n);break;case 22:Et=(l=Et)||n.memoizedState!==null,nn(t,e,n),Et=l;break;default:nn(t,e,n)}}function Qg(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Lr(t)}catch(n){ot(e,e.return,n)}}function qv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Ah),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Ah),e;default:throw Error(w(435,t.tag))}}function pc(t,e){var n=qv(t);e.forEach(function(l){var i=Kv.bind(null,t,l);n.has(l)||(n.add(l),l.then(i,i))})}function fe(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],r=t,a=e,u=a;t:for(;u!==null;){switch(u.tag){case 27:if(Fn(u.type)){pt=u.stateNode,ie=!1;break t}break;case 5:pt=u.stateNode,ie=!1;break t;case 3:case 4:pt=u.stateNode.containerInfo,ie=!0;break t}u=u.return}if(pt===null)throw Error(w(160));Gg(r,a,i),pt=null,ie=!1,r=i.alternate,r!==null&&(r.return=null),i.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Zg(e,t),e=e.sibling}var Ne=null;function Zg(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:fe(e,t),me(t),l&4&&(Gn(3,t,t.return),Kr(3,t),Gn(5,t,t.return));break;case 1:fe(e,t),me(t),l&512&&(Et||n===null||Ye(n,n.return)),l&64&&ln&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=Ne;if(fe(e,t),me(t),l&512&&(Et||n===null||Ye(n,n.return)),l&4){var r=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,i=i.ownerDocument||i;e:switch(l){case"title":r=i.getElementsByTagName("title")[0],(!r||r[jr]||r[Kt]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=i.createElement(l),i.head.insertBefore(r,i.querySelector("head > title"))),Qt(r,l,n),r[Kt]=t,Ut(r),l=r;break t;case"link":var a=Gh("link","href",i).get(l+(n.href||""));if(a){for(var u=0;u<a.length;u++)if(r=a[u],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){a.splice(u,1);break e}}r=i.createElement(l),Qt(r,l,n),i.head.appendChild(r);break;case"meta":if(a=Gh("meta","content",i).get(l+(n.content||""))){for(u=0;u<a.length;u++)if(r=a[u],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){a.splice(u,1);break e}}r=i.createElement(l),Qt(r,l,n),i.head.appendChild(r);break;default:throw Error(w(468,l))}r[Kt]=t,Ut(r),l=r}t.stateNode=l}else Qh(i,t.type,t.stateNode);else t.stateNode=Xh(i,l,t.memoizedProps);else r!==l?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,l===null?Qh(i,t.type,t.stateNode):Xh(i,l,t.memoizedProps)):l===null&&t.stateNode!==null&&sc(t,t.memoizedProps,n.memoizedProps)}break;case 27:fe(e,t),me(t),l&512&&(Et||n===null||Ye(n,n.return)),n!==null&&l&4&&sc(t,t.memoizedProps,n.memoizedProps);break;case 5:if(fe(e,t),me(t),l&512&&(Et||n===null||Ye(n,n.return)),t.flags&32){i=t.stateNode;try{yi(i,"")}catch(m){ot(t,t.return,m)}}l&4&&t.stateNode!=null&&(i=t.memoizedProps,sc(t,i,n!==null?n.memoizedProps:i)),l&1024&&(mc=!0);break;case 6:if(fe(e,t),me(t),l&4){if(t.stateNode===null)throw Error(w(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(m){ot(t,t.return,m)}}break;case 3:if(Ia=null,i=Ne,Ne=ku(e.containerInfo),fe(e,t),Ne=i,me(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Lr(e.containerInfo)}catch(m){ot(t,t.return,m)}mc&&(mc=!1,Fg(t));break;case 4:l=Ne,Ne=ku(t.stateNode.containerInfo),fe(e,t),me(t),Ne=l;break;case 12:fe(e,t),me(t);break;case 13:fe(e,t),me(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&($s=Xe()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,pc(t,l)));break;case 22:i=t.memoizedState!==null;var o=n!==null&&n.memoizedState!==null,c=ln,s=Et;if(ln=c||i,Et=s||o,fe(e,t),Et=s,ln=c,me(t),l&8192)t:for(e=t.stateNode,e._visibility=i?e._visibility&-2:e._visibility|1,i&&(n===null||o||ln||Et||ul(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){o=n=e;try{if(r=o.stateNode,i)a=r.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none";else{u=o.stateNode;var f=o.memoizedProps.style,p=f!=null&&f.hasOwnProperty("display")?f.display:null;u.style.display=p==null||typeof p=="boolean"?"":(""+p).trim()}}catch(m){ot(o,o.return,m)}}}else if(e.tag===6){if(n===null){o=e;try{o.stateNode.nodeValue=i?"":o.memoizedProps}catch(m){ot(o,o.return,m)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,pc(t,n))));break;case 19:fe(e,t),me(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,pc(t,l)));break;case 30:break;case 21:break;default:fe(e,t),me(t)}}function me(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(jg(l)){n=l;break}l=l.return}if(n==null)throw Error(w(160));switch(n.tag){case 27:var i=n.stateNode,r=fc(t);du(t,r,i);break;case 5:var a=n.stateNode;n.flags&32&&(yi(a,""),n.flags&=-33);var u=fc(t);du(t,u,a);break;case 3:case 4:var o=n.stateNode.containerInfo,c=fc(t);Pc(t,c,o);break;default:throw Error(w(161))}}catch(s){ot(t,t.return,s)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Fg(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Fg(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function En(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Vg(t,e.alternate,e),e=e.sibling}function ul(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Gn(4,e,e.return),ul(e);break;case 1:Ye(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Hg(e,e.return,n),ul(e);break;case 27:Sr(e.stateNode);case 26:case 5:Ye(e,e.return),ul(e);break;case 22:e.memoizedState===null&&ul(e);break;case 30:ul(e);break;default:ul(e)}t=t.sibling}}function Tn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,i=t,r=e,a=r.flags;switch(r.tag){case 0:case 11:case 15:Tn(i,r,n),Kr(4,r);break;case 1:if(Tn(i,r,n),l=r,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(c){ot(l,l.return,c)}if(l=r,i=l.updateQueue,i!==null){var u=l.stateNode;try{var o=i.shared.hiddenCallbacks;if(o!==null)for(i.shared.hiddenCallbacks=null,i=0;i<o.length;i++)Vd(o[i],u)}catch(c){ot(l,l.return,c)}}n&&a&64&&Bg(r),yr(r,r.return);break;case 27:Yg(r);case 26:case 5:Tn(i,r,n),n&&l===null&&a&4&&qg(r),yr(r,r.return);break;case 12:Tn(i,r,n);break;case 13:Tn(i,r,n),n&&a&4&&Qg(i,r);break;case 22:r.memoizedState===null&&Tn(i,r,n),yr(r,r.return);break;case 30:break;default:Tn(i,r,n)}e=e.sibling}}function Is(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Gr(n))}function Js(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Gr(t))}function qe(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Kg(t,e,n,l),e=e.sibling}function Kg(t,e,n,l){var i=e.flags;switch(e.tag){case 0:case 11:case 15:qe(t,e,n,l),i&2048&&Kr(9,e);break;case 1:qe(t,e,n,l);break;case 3:qe(t,e,n,l),i&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Gr(t)));break;case 12:if(i&2048){qe(t,e,n,l),t=e.stateNode;try{var r=e.memoizedProps,a=r.id,u=r.onPostCommit;typeof u=="function"&&u(a,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(o){ot(e,e.return,o)}}else qe(t,e,n,l);break;case 13:qe(t,e,n,l);break;case 23:break;case 22:r=e.stateNode,a=e.alternate,e.memoizedState!==null?r._visibility&2?qe(t,e,n,l):xr(t,e):r._visibility&2?qe(t,e,n,l):(r._visibility|=2,Fl(t,e,n,l,(e.subtreeFlags&10256)!==0)),i&2048&&Is(a,e);break;case 24:qe(t,e,n,l),i&2048&&Js(e.alternate,e);break;default:qe(t,e,n,l)}}function Fl(t,e,n,l,i){for(i=i&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,a=e,u=n,o=l,c=a.flags;switch(a.tag){case 0:case 11:case 15:Fl(r,a,u,o,i),Kr(8,a);break;case 23:break;case 22:var s=a.stateNode;a.memoizedState!==null?s._visibility&2?Fl(r,a,u,o,i):xr(r,a):(s._visibility|=2,Fl(r,a,u,o,i)),i&&c&2048&&Is(a.alternate,a);break;case 24:Fl(r,a,u,o,i),i&&c&2048&&Js(a.alternate,a);break;default:Fl(r,a,u,o,i)}e=e.sibling}}function xr(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,i=l.flags;switch(l.tag){case 22:xr(n,l),i&2048&&Is(l.alternate,l);break;case 24:xr(n,l),i&2048&&Js(l.alternate,l);break;default:xr(n,l)}e=e.sibling}}var ur=8192;function Gl(t){if(t.subtreeFlags&ur)for(t=t.child;t!==null;)Ig(t),t=t.sibling}function Ig(t){switch(t.tag){case 26:Gl(t),t.flags&ur&&t.memoizedState!==null&&kS(Ne,t.memoizedState,t.memoizedProps);break;case 5:Gl(t);break;case 3:case 4:var e=Ne;Ne=ku(t.stateNode.containerInfo),Gl(t),Ne=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=ur,ur=16777216,Gl(t),ur=e):Gl(t));break;default:Gl(t)}}function Jg(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function er(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Lt=l,Pg(l,t)}Jg(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Wg(t),t=t.sibling}function Wg(t){switch(t.tag){case 0:case 11:case 15:er(t),t.flags&2048&&Gn(9,t,t.return);break;case 3:er(t);break;case 12:er(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Fa(t)):er(t);break;default:er(t)}}function Fa(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Lt=l,Pg(l,t)}Jg(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Gn(8,e,e.return),Fa(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Fa(e));break;default:Fa(e)}t=t.sibling}}function Pg(t,e){for(;Lt!==null;){var n=Lt;switch(n.tag){case 0:case 11:case 15:Gn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Gr(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Lt=l;else t:for(n=t;Lt!==null;){l=Lt;var i=l.sibling,r=l.return;if(Xg(l),l===n){Lt=null;break t}if(i!==null){i.return=r,Lt=i;break t}Lt=r}}}var jv={getCacheForType:function(t){var e=It(Mt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},Yv=typeof WeakMap=="function"?WeakMap:Map,nt=0,ct=null,K=null,W=0,et=0,pe=null,Rn=!1,Di=!1,Ws=!1,dn=0,bt=0,Qn=0,pl=0,Ps=0,ze=0,ki=0,br=null,re=null,$c=!1,$s=0,gu=1/0,yu=null,Hn=null,Gt=0,qn=null,Ei=null,hi=0,ts=0,es=null,$g=null,vr=0,ns=null;function ye(){if(nt&2&&W!==0)return W&-W;if(H.T!==null){var t=xi;return t!==0?t:ef()}return sd()}function ty(){ze===0&&(ze=!(W&536870912)||$?ad():536870912);var t=Ce.current;return t!==null&&(t.flags|=32),ze}function xe(t,e,n){(t===ct&&(et===2||et===9)||t.cancelPendingCommit!==null)&&(Ti(t,0),Nn(t,W,ze,!1)),qr(t,n),(!(nt&2)||t!==ct)&&(t===ct&&(!(nt&2)&&(pl|=n),bt===4&&Nn(t,W,ze,!1)),Ze(t))}function ey(t,e,n){if(nt&6)throw Error(w(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||Hr(t,e),i=l?Gv(t,e):hc(t,e,!0),r=l;do{if(i===0){Di&&!l&&Nn(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!Vv(n)){i=hc(t,e,!1),r=!1;continue}if(i===2){if(r=e,t.errorRecoveryDisabledLanes&r)var a=0;else a=t.pendingLanes&-536870913,a=a!==0?a:a&536870912?536870912:0;if(a!==0){e=a;t:{var u=t;i=br;var o=u.current.memoizedState.isDehydrated;if(o&&(Ti(u,a).flags|=256),a=hc(u,a,!1),a!==2){if(Ws&&!o){u.errorRecoveryDisabledLanes|=r,pl|=r,i=4;break t}r=re,re=i,r!==null&&(re===null?re=r:re.push.apply(re,r))}i=a}if(r=!1,i!==2)continue}}if(i===1){Ti(t,0),Nn(t,e,0,!0);break}t:{switch(l=t,r=i,r){case 0:case 1:throw Error(w(345));case 4:if((e&4194048)!==e)break;case 6:Nn(l,e,ze,!Rn);break t;case 2:re=null;break;case 3:case 5:break;default:throw Error(w(329))}if((e&62914560)===e&&(i=$s+300-Xe(),10<i)){if(Nn(l,e,ze,!Rn),zu(l,0,!0)!==0)break t;l.timeoutHandle=vy(wh.bind(null,l,n,re,yu,$c,e,ze,pl,ki,Rn,r,2,-0,0),i);break t}wh(l,n,re,yu,$c,e,ze,pl,ki,Rn,r,0,-0,0)}}break}while(!0);Ze(t)}function wh(t,e,n,l,i,r,a,u,o,c,s,f,p,m){if(t.timeoutHandle=-1,f=e.subtreeFlags,(f&8192||(f&16785408)===16785408)&&(Or={stylesheets:null,count:0,unsuspend:SS},Ig(e),f=ES(),f!==null)){t.cancelPendingCommit=f(Ch.bind(null,t,e,r,n,l,i,a,u,o,s,1,p,m)),Nn(t,r,a,!c);return}Ch(t,e,r,n,l,i,a,u,o)}function Vv(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],r=i.getSnapshot;i=i.value;try{if(!be(r(),i))return!1}catch(a){return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Nn(t,e,n,l){e&=~Ps,e&=~pl,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var i=e;0<i;){var r=31-ge(i),a=1<<r;l[r]=-1,i&=~a}n!==0&&od(t,n,e)}function Bu(){return nt&6?!0:(Ir(0,!1),!1)}function tf(){if(K!==null){if(et===0)var t=K.return;else t=K,on=kl=null,js(t),pi=null,Cr=0,t=K;for(;t!==null;)Ug(t.alternate,t),t=t.return;K=null}}function Ti(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,iS(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),tf(),ct=t,K=n=sn(t.current,null),W=e,et=0,pe=null,Rn=!1,Di=Hr(t,e),Ws=!1,ki=ze=Ps=pl=Qn=bt=0,re=br=null,$c=!1,e&8&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var i=31-ge(l),r=1<<i;e|=t[i],l&=~r}return dn=e,Ou(),n}function ny(t,e){G=null,H.H=su,e===Qr||e===Ru?(e=ih(),et=3):e===jd?(e=ih(),et=4):et=e===Mg?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,pe=e,K===null&&(bt=1,pu(t,we(e,t.current)))}function ly(){var t=H.H;return H.H=su,t===null?su:t}function iy(){var t=H.A;return H.A=jv,t}function ls(){bt=4,Rn||(W&4194048)!==W&&Ce.current!==null||(Di=!0),!(Qn&134217727)&&!(pl&134217727)||ct===null||Nn(ct,W,ze,!1)}function hc(t,e,n){var l=nt;nt|=2;var i=ly(),r=iy();(ct!==t||W!==e)&&(yu=null,Ti(t,e)),e=!1;var a=bt;t:do try{if(et!==0&&K!==null){var u=K,o=pe;switch(et){case 8:tf(),a=6;break t;case 3:case 2:case 9:case 6:Ce.current===null&&(e=!0);var c=et;if(et=0,pe=null,ai(t,u,o,c),n&&Di){a=0;break t}break;default:c=et,et=0,pe=null,ai(t,u,o,c)}}Xv(),a=bt;break}catch(s){ny(t,s)}while(!0);return e&&t.shellSuspendCounter++,on=kl=null,nt=l,H.H=i,H.A=r,K===null&&(ct=null,W=0,Ou()),a}function Xv(){for(;K!==null;)ry(K)}function Gv(t,e){var n=nt;nt|=2;var l=ly(),i=iy();ct!==t||W!==e?(yu=null,gu=Xe()+500,Ti(t,e)):Di=Hr(t,e);t:do try{if(et!==0&&K!==null){e=K;var r=pe;e:switch(et){case 1:et=0,pe=null,ai(t,e,r,1);break;case 2:case 9:if(lh(r)){et=0,pe=null,zh(e);break}e=function(){et!==2&&et!==9||ct!==t||(et=7),Ze(t)},r.then(e,e);break t;case 3:et=7;break t;case 4:et=5;break t;case 7:lh(r)?(et=0,pe=null,zh(e)):(et=0,pe=null,ai(t,e,r,7));break;case 5:var a=null;switch(K.tag){case 26:a=K.memoizedState;case 5:case 27:var u=K;if(!a||Ty(a)){et=0,pe=null;var o=u.sibling;if(o!==null)K=o;else{var c=u.return;c!==null?(K=c,Hu(c)):K=null}break e}}et=0,pe=null,ai(t,e,r,5);break;case 6:et=0,pe=null,ai(t,e,r,6);break;case 8:tf(),bt=6;break t;default:throw Error(w(462))}}Qv();break}catch(s){ny(t,s)}while(!0);return on=kl=null,H.H=l,H.A=i,nt=n,K!==null?0:(ct=null,W=0,Ou(),bt)}function Qv(){for(;K!==null&&!pb();)ry(K)}function ry(t){var e=Lg(t.alternate,t,dn);t.memoizedProps=t.pendingProps,e===null?Hu(t):K=e}function zh(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=vh(n,e,e.pendingProps,e.type,void 0,W);break;case 11:e=vh(n,e,e.pendingProps,e.type.render,e.ref,W);break;case 5:js(e);default:Ug(n,e),e=K=Ud(e,dn),e=Lg(n,e,dn)}t.memoizedProps=t.pendingProps,e===null?Hu(t):K=e}function ai(t,e,n,l){on=kl=null,js(e),pi=null,Cr=0;var i=e.return;try{if(Nv(t,i,e,n,W)){bt=1,pu(t,we(n,t.current)),K=null;return}}catch(r){if(i!==null)throw K=i,r;bt=1,pu(t,we(n,t.current)),K=null;return}e.flags&32768?($||l===1?t=!0:Di||W&536870912?t=!1:(Rn=t=!0,(l===2||l===9||l===3||l===6)&&(l=Ce.current,l!==null&&l.tag===13&&(l.flags|=16384))),ay(e,t)):Hu(e)}function Hu(t){var e=t;do{if(e.flags&32768){ay(e,Rn);return}t=e.return;var n=Uv(e.alternate,e,dn);if(n!==null){K=n;return}if(e=e.sibling,e!==null){K=e;return}K=e=t}while(e!==null);bt===0&&(bt=5)}function ay(t,e){do{var n=Bv(t.alternate,t);if(n!==null){n.flags&=32767,K=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){K=t;return}K=t=n}while(t!==null);bt=6,K=null}function Ch(t,e,n,l,i,r,a,u,o){t.cancelPendingCommit=null;do qu();while(Gt!==0);if(nt&6)throw Error(w(327));if(e!==null){if(e===t.current)throw Error(w(177));if(r=e.lanes|e.childLanes,r|=zs,Eb(t,n,r,a,u,o),t===ct&&(K=ct=null,W=0),Ei=e,qn=t,hi=n,ts=r,es=i,$g=l,e.subtreeFlags&10256||e.flags&10256?(t.callbackNode=null,t.callbackPriority=0,Iv(tu,function(){return fy(!0),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,e.subtreeFlags&13878||l){l=H.T,H.T=null,i=tt.p,tt.p=2,a=nt,nt|=4;try{Hv(t,e,n)}finally{nt=a,tt.p=i,H.T=l}}Gt=1,uy(),oy(),cy()}}function uy(){if(Gt===1){Gt=0;var t=qn,e=Ei,n=(e.flags&13878)!==0;if(e.subtreeFlags&13878||n){n=H.T,H.T=null;var l=tt.p;tt.p=2;var i=nt;nt|=4;try{Zg(e,t);var r=us,a=Cd(t.containerInfo),u=r.focusedElem,o=r.selectionRange;if(a!==u&&u&&u.ownerDocument&&zd(u.ownerDocument.documentElement,u)){if(o!==null&&ws(u)){var c=o.start,s=o.end;if(s===void 0&&(s=c),"selectionStart"in u)u.selectionStart=c,u.selectionEnd=Math.min(s,u.value.length);else{var f=u.ownerDocument||document,p=f&&f.defaultView||window;if(p.getSelection){var m=p.getSelection(),y=u.textContent.length,v=Math.min(o.start,y),T=o.end===void 0?v:Math.min(o.end,y);!m.extend&&v>T&&(a=T,T=v,v=a);var h=Ip(u,v),d=Ip(u,T);if(h&&d&&(m.rangeCount!==1||m.anchorNode!==h.node||m.anchorOffset!==h.offset||m.focusNode!==d.node||m.focusOffset!==d.offset)){var g=f.createRange();g.setStart(h.node,h.offset),m.removeAllRanges(),v>T?(m.addRange(g),m.extend(d.node,d.offset)):(g.setEnd(d.node,d.offset),m.addRange(g))}}}}for(f=[],m=u;m=m.parentNode;)m.nodeType===1&&f.push({element:m,left:m.scrollLeft,top:m.scrollTop});for(typeof u.focus=="function"&&u.focus(),u=0;u<f.length;u++){var k=f[u];k.element.scrollLeft=k.left,k.element.scrollTop=k.top}}Au=!!as,us=as=null}finally{nt=i,tt.p=l,H.T=n}}t.current=e,Gt=2}}function oy(){if(Gt===2){Gt=0;var t=qn,e=Ei,n=(e.flags&8772)!==0;if(e.subtreeFlags&8772||n){n=H.T,H.T=null;var l=tt.p;tt.p=2;var i=nt;nt|=4;try{Vg(t,e.alternate,e)}finally{nt=i,tt.p=l,H.T=n}}Gt=3}}function cy(){if(Gt===4||Gt===3){Gt=0,hb();var t=qn,e=Ei,n=hi,l=$g;e.subtreeFlags&10256||e.flags&10256?Gt=5:(Gt=0,Ei=qn=null,sy(t,t.pendingLanes));var i=t.pendingLanes;if(i===0&&(Hn=null),bs(n),e=e.stateNode,de&&typeof de.onCommitFiberRoot=="function")try{de.onCommitFiberRoot(Br,e,void 0,(e.current.flags&128)===128)}catch(o){}if(l!==null){e=H.T,i=tt.p,tt.p=2,H.T=null;try{for(var r=t.onRecoverableError,a=0;a<l.length;a++){var u=l[a];r(u.value,{componentStack:u.stack})}}finally{H.T=e,tt.p=i}}hi&3&&qu(),Ze(t),i=t.pendingLanes,n&4194090&&i&42?t===ns?vr++:(vr=0,ns=t):vr=0,Ir(0,!1)}}function sy(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Gr(e)))}function qu(t){return uy(),oy(),cy(),fy(t)}function fy(){if(Gt!==5)return!1;var t=qn,e=ts;ts=0;var n=bs(hi),l=H.T,i=tt.p;try{tt.p=32>n?32:n,H.T=null,n=es,es=null;var r=qn,a=hi;if(Gt=0,Ei=qn=null,hi=0,nt&6)throw Error(w(331));var u=nt;if(nt|=4,Wg(r.current),Kg(r,r.current,a,n),nt=u,Ir(0,!1),de&&typeof de.onPostCommitFiberRoot=="function")try{de.onPostCommitFiberRoot(Br,r)}catch(o){}return!0}finally{tt.p=i,H.T=l,sy(t,e)}}function Dh(t,e,n){e=we(n,e),e=Ic(t.stateNode,e,2),t=Bn(t,e,2),t!==null&&(qr(t,2),Ze(t))}function ot(t,e,n){if(t.tag===3)Dh(t,t,n);else for(;e!==null;){if(e.tag===3){Dh(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Hn===null||!Hn.has(l))){t=we(n,t),n=Cg(2),l=Bn(e,n,2),l!==null&&(Dg(n,l,e,t),qr(l,2),Ze(l));break}}e=e.return}}function dc(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new Yv;var i=new Set;l.set(e,i)}else i=l.get(e),i===void 0&&(i=new Set,l.set(e,i));i.has(n)||(Ws=!0,i.add(n),t=Zv.bind(null,t,e,n),e.then(t,t))}function Zv(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,ct===t&&(W&n)===n&&(bt===4||bt===3&&(W&62914560)===W&&300>Xe()-$s?!(nt&2)&&Ti(t,0):Ps|=n,ki===W&&(ki=0)),Ze(t)}function my(t,e){e===0&&(e=ud()),t=Ci(t,e),t!==null&&(qr(t,e),Ze(t))}function Fv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),my(t,n)}function Kv(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(w(314))}l!==null&&l.delete(e),my(t,n)}function Iv(t,e){return ys(t,e)}var xu=null,Kl=null,is=!1,bu=!1,gc=!1,hl=0;function Ze(t){t!==Kl&&t.next===null&&(Kl===null?xu=Kl=t:Kl=Kl.next=t),bu=!0,is||(is=!0,Wv())}function Ir(t,e){if(!gc&&bu){gc=!0;do for(var n=!1,l=xu;l!==null;){if(!e)if(t!==0){var i=l.pendingLanes;if(i===0)var r=0;else{var a=l.suspendedLanes,u=l.pingedLanes;r=(1<<31-ge(42|t)+1)-1,r&=i&~(a&~u),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,Mh(l,r))}else r=W,r=zu(l,l===ct?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),!(r&3)||Hr(l,r)||(n=!0,Mh(l,r));l=l.next}while(n);gc=!1}}function Jv(){py()}function py(){bu=is=!1;var t=0;hl!==0&&(lS()&&(t=hl),hl=0);for(var e=Xe(),n=null,l=xu;l!==null;){var i=l.next,r=hy(l,e);r===0?(l.next=null,n===null?xu=i:n.next=i,i===null&&(Kl=n)):(n=l,(t!==0||r&3)&&(bu=!0)),l=i}Ir(t,!1)}function hy(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,i=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var a=31-ge(r),u=1<<a,o=i[a];o===-1?(!(u&n)||u&l)&&(i[a]=kb(u,e)):o<=e&&(t.expiredLanes|=u),r&=~u}if(e=ct,n=W,n=zu(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(et===2||et===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&Xo(l),t.callbackNode=null,t.callbackPriority=0;if(!(n&3)||Hr(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&Xo(l),bs(n)){case 2:case 8:n=id;break;case 32:n=tu;break;case 268435456:n=rd;break;default:n=tu}return l=dy.bind(null,t),n=ys(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&Xo(l),t.callbackPriority=2,t.callbackNode=null,2}function dy(t,e){if(Gt!==0&&Gt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(qu(!0)&&t.callbackNode!==n)return null;var l=W;return l=zu(t,t===ct?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(ey(t,l,e),hy(t,Xe()),t.callbackNode!=null&&t.callbackNode===n?dy.bind(null,t):null)}function Mh(t,e){if(qu())return null;ey(t,e,!0)}function Wv(){rS(function(){nt&6?ys(ld,Jv):py()})}function ef(){return hl===0&&(hl=ad()),hl}function Oh(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:qa(""+t)}function _h(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Pv(t,e,n,l,i){if(e==="submit"&&n&&n.stateNode===i){var r=Oh((i[ae]||null).action),a=l.submitter;a&&(e=(e=a[ae]||null)?Oh(e.formAction):a.getAttribute("formAction"),e!==null&&(r=e,a=null));var u=new Cu("action","action",null,l,i);t.push({event:u,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(hl!==0){var o=a?_h(i,a):new FormData(i);Fc(n,{pending:!0,data:o,method:i.method,action:r},null,o)}}else typeof r=="function"&&(u.preventDefault(),o=a?_h(i,a):new FormData(i),Fc(n,{pending:!0,data:o,method:i.method,action:r},r,o))},currentTarget:i}]})}}for(Ra=0;Ra<Lc.length;Ra++)Na=Lc[Ra],Rh=Na.toLowerCase(),Nh=Na[0].toUpperCase()+Na.slice(1),Ue(Rh,"on"+Nh);var Na,Rh,Nh,Ra;Ue(Md,"onAnimationEnd");Ue(Od,"onAnimationIteration");Ue(_d,"onAnimationStart");Ue("dblclick","onDoubleClick");Ue("focusin","onFocus");Ue("focusout","onBlur");Ue(yv,"onTransitionRun");Ue(xv,"onTransitionStart");Ue(bv,"onTransitionCancel");Ue(Rd,"onTransitionEnd");gi("onMouseEnter",["mouseout","mouseover"]);gi("onMouseLeave",["mouseout","mouseover"]);gi("onPointerEnter",["pointerout","pointerover"]);gi("onPointerLeave",["pointerout","pointerover"]);bl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));bl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));bl("onBeforeInput",["compositionend","keypress","textInput","paste"]);bl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));bl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));bl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),$v=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Dr));function gy(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],i=l.event;l=l.listeners;t:{var r=void 0;if(e)for(var a=l.length-1;0<=a;a--){var u=l[a],o=u.instance,c=u.currentTarget;if(u=u.listener,o!==r&&i.isPropagationStopped())break t;r=u,i.currentTarget=c;try{r(i)}catch(s){mu(s)}i.currentTarget=null,r=o}else for(a=0;a<l.length;a++){if(u=l[a],o=u.instance,c=u.currentTarget,u=u.listener,o!==r&&i.isPropagationStopped())break t;r=u,i.currentTarget=c;try{r(i)}catch(s){mu(s)}i.currentTarget=null,r=o}}}}function F(t,e){var n=e[Cc];n===void 0&&(n=e[Cc]=new Set);var l=t+"__bubble";n.has(l)||(yy(e,t,2,!1),n.add(l))}function yc(t,e,n){var l=0;e&&(l|=4),yy(n,t,l,e)}var La="_reactListening"+Math.random().toString(36).slice(2);function nf(t){if(!t[La]){t[La]=!0,fd.forEach(function(n){n!=="selectionchange"&&($v.has(n)||yc(n,!1,t),yc(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[La]||(e[La]=!0,yc("selectionchange",!1,e))}}function yy(t,e,n,l){switch(Dy(e)){case 2:var i=wS;break;case 8:i=zS;break;default:i=uf}n=i.bind(null,e,n,t),i=void 0,!_c||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),l?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function xc(t,e,n,l,i){var r=l;if(!(e&1)&&!(e&2)&&l!==null)t:for(;;){if(l===null)return;var a=l.tag;if(a===3||a===4){var u=l.stateNode.containerInfo;if(u===i)break;if(a===4)for(a=l.return;a!==null;){var o=a.tag;if((o===3||o===4)&&a.stateNode.containerInfo===i)return;a=a.return}for(;u!==null;){if(a=Wl(u),a===null)return;if(o=a.tag,o===5||o===6||o===26||o===27){l=r=a;continue t}u=u.parentNode}}l=l.return}bd(function(){var c=r,s=ks(n),f=[];t:{var p=Nd.get(t);if(p!==void 0){var m=Cu,y=t;switch(t){case"keypress":if(Ya(n)===0)break t;case"keydown":case"keyup":m=Ib;break;case"focusin":y="focus",m=Wo;break;case"focusout":y="blur",m=Wo;break;case"beforeblur":case"afterblur":m=Wo;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=jp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Bb;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Pb;break;case Md:case Od:case _d:m=jb;break;case Rd:m=tv;break;case"scroll":case"scrollend":m=Lb;break;case"wheel":m=nv;break;case"copy":case"cut":case"paste":m=Vb;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Vp;break;case"toggle":case"beforetoggle":m=iv}var v=(e&4)!==0,T=!v&&(t==="scroll"||t==="scrollend"),h=v?p!==null?p+"Capture":null:p;v=[];for(var d=c,g;d!==null;){var k=d;if(g=k.stateNode,k=k.tag,k!==5&&k!==26&&k!==27||g===null||h===null||(k=Er(d,h),k!=null&&v.push(Mr(d,k,g))),T)break;d=d.return}0<v.length&&(p=new m(p,y,null,n,s),f.push({event:p,listeners:v}))}}if(!(e&7)){t:{if(p=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",p&&n!==Oc&&(y=n.relatedTarget||n.fromElement)&&(Wl(y)||y[wi]))break t;if((m||p)&&(p=s.window===s?s:(p=s.ownerDocument)?p.defaultView||p.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=c,y=y?Wl(y):null,y!==null&&(T=Ur(y),v=y.tag,y!==T||v!==5&&v!==27&&v!==6)&&(y=null)):(m=null,y=c),m!==y)){if(v=jp,k="onMouseLeave",h="onMouseEnter",d="mouse",(t==="pointerout"||t==="pointerover")&&(v=Vp,k="onPointerLeave",h="onPointerEnter",d="pointer"),T=m==null?p:ar(m),g=y==null?p:ar(y),p=new v(k,d+"leave",m,n,s),p.target=T,p.relatedTarget=g,k=null,Wl(s)===c&&(v=new v(h,d+"enter",y,n,s),v.target=g,v.relatedTarget=T,k=v),T=k,m&&y)e:{for(v=m,h=y,d=0,g=v;g;g=Ql(g))d++;for(g=0,k=h;k;k=Ql(k))g++;for(;0<d-g;)v=Ql(v),d--;for(;0<g-d;)h=Ql(h),g--;for(;d--;){if(v===h||h!==null&&v===h.alternate)break e;v=Ql(v),h=Ql(h)}v=null}else v=null;m!==null&&Lh(f,p,m,v,!1),y!==null&&T!==null&&Lh(f,T,y,v,!0)}}t:{if(p=c?ar(c):window,m=p.nodeName&&p.nodeName.toLowerCase(),m==="select"||m==="input"&&p.type==="file")var z=Zp;else if(Qp(p))if(Ad)z=hv;else{z=mv;var E=fv}else m=p.nodeName,!m||m.toLowerCase()!=="input"||p.type!=="checkbox"&&p.type!=="radio"?c&&Ss(c.elementType)&&(z=Zp):z=pv;if(z&&(z=z(t,c))){Td(f,z,n,s);break t}E&&E(t,p,c),t==="focusout"&&c&&p.type==="number"&&c.memoizedProps.value!=null&&Mc(p,"number",p.value)}switch(E=c?ar(c):window,t){case"focusin":(Qp(E)||E.contentEditable==="true")&&(ti=E,Rc=c,sr=null);break;case"focusout":sr=Rc=ti=null;break;case"mousedown":Nc=!0;break;case"contextmenu":case"mouseup":case"dragend":Nc=!1,Jp(f,n,s);break;case"selectionchange":if(gv)break;case"keydown":case"keyup":Jp(f,n,s)}var M;if(As)t:{switch(t){case"compositionstart":var O="onCompositionStart";break t;case"compositionend":O="onCompositionEnd";break t;case"compositionupdate":O="onCompositionUpdate";break t}O=void 0}else $l?kd(t,n)&&(O="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(O="onCompositionStart");O&&(Sd&&n.locale!=="ko"&&($l||O!=="onCompositionStart"?O==="onCompositionEnd"&&$l&&(M=vd()):(_n=s,Es="value"in _n?_n.value:_n.textContent,$l=!0)),E=vu(c,O),0<E.length&&(O=new Yp(O,t,null,n,s),f.push({event:O,listeners:E}),M?O.data=M:(M=Ed(n),M!==null&&(O.data=M)))),(M=av?uv(t,n):ov(t,n))&&(O=vu(c,"onBeforeInput"),0<O.length&&(E=new Yp("onBeforeInput","beforeinput",null,n,s),f.push({event:E,listeners:O}),E.data=M)),Pv(f,t,c,n,s)}gy(f,e)})}function Mr(t,e,n){return{instance:t,listener:e,currentTarget:n}}function vu(t,e){for(var n=e+"Capture",l=[];t!==null;){var i=t,r=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||r===null||(i=Er(t,n),i!=null&&l.unshift(Mr(t,i,r)),i=Er(t,e),i!=null&&l.push(Mr(t,i,r))),t.tag===3)return l;t=t.return}return[]}function Ql(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Lh(t,e,n,l,i){for(var r=e._reactName,a=[];n!==null&&n!==l;){var u=n,o=u.alternate,c=u.stateNode;if(u=u.tag,o!==null&&o===l)break;u!==5&&u!==26&&u!==27||c===null||(o=c,i?(c=Er(n,r),c!=null&&a.unshift(Mr(n,c,o))):i||(c=Er(n,r),c!=null&&a.push(Mr(n,c,o)))),n=n.return}a.length!==0&&t.push({event:e,listeners:a})}var tS=/\r\n?/g,eS=/\u0000|\uFFFD/g;function Uh(t){return(typeof t=="string"?t:""+t).replace(tS,`
`).replace(eS,"")}function xy(t,e){return e=Uh(e),Uh(t)===e}function ju(){}function rt(t,e,n,l,i,r){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||yi(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&yi(t,""+l);break;case"className":Ta(t,"class",l);break;case"tabIndex":Ta(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Ta(t,n,l);break;case"style":xd(t,l,r);break;case"data":if(e!=="object"){Ta(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=qa(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&rt(t,e,"name",i.name,i,null),rt(t,e,"formEncType",i.formEncType,i,null),rt(t,e,"formMethod",i.formMethod,i,null),rt(t,e,"formTarget",i.formTarget,i,null)):(rt(t,e,"encType",i.encType,i,null),rt(t,e,"method",i.method,i,null),rt(t,e,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=qa(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=ju);break;case"onScroll":l!=null&&F("scroll",t);break;case"onScrollEnd":l!=null&&F("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(w(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(w(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=qa(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":F("beforetoggle",t),F("toggle",t),Ha(t,"popover",l);break;case"xlinkActuate":tn(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":tn(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":tn(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":tn(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":tn(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":tn(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":tn(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":tn(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":tn(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Ha(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Rb.get(n)||n,Ha(t,n,l))}}function rs(t,e,n,l,i,r){switch(n){case"style":xd(t,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(w(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(w(60));t.innerHTML=n}}break;case"children":typeof l=="string"?yi(t,l):(typeof l=="number"||typeof l=="bigint")&&yi(t,""+l);break;case"onScroll":l!=null&&F("scroll",t);break;case"onScrollEnd":l!=null&&F("scrollend",t);break;case"onClick":l!=null&&(t.onclick=ju);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!md.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),e=n.slice(2,i?n.length-7:void 0),r=t[ae]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,i),typeof l=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,i);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Ha(t,n,l)}}}function Qt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":F("error",t),F("load",t);var l=!1,i=!1,r;for(r in n)if(n.hasOwnProperty(r)){var a=n[r];if(a!=null)switch(r){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(w(137,e));default:rt(t,e,r,a,n,null)}}i&&rt(t,e,"srcSet",n.srcSet,n,null),l&&rt(t,e,"src",n.src,n,null);return;case"input":F("invalid",t);var u=r=a=i=null,o=null,c=null;for(l in n)if(n.hasOwnProperty(l)){var s=n[l];if(s!=null)switch(l){case"name":i=s;break;case"type":a=s;break;case"checked":o=s;break;case"defaultChecked":c=s;break;case"value":r=s;break;case"defaultValue":u=s;break;case"children":case"dangerouslySetInnerHTML":if(s!=null)throw Error(w(137,e));break;default:rt(t,e,l,s,n,null)}}dd(t,r,u,o,c,a,i,!1),eu(t);return;case"select":F("invalid",t),l=a=r=null;for(i in n)if(n.hasOwnProperty(i)&&(u=n[i],u!=null))switch(i){case"value":r=u;break;case"defaultValue":a=u;break;case"multiple":l=u;default:rt(t,e,i,u,n,null)}e=r,n=a,t.multiple=!!l,e!=null?oi(t,!!l,e,!1):n!=null&&oi(t,!!l,n,!0);return;case"textarea":F("invalid",t),r=i=l=null;for(a in n)if(n.hasOwnProperty(a)&&(u=n[a],u!=null))switch(a){case"value":l=u;break;case"defaultValue":i=u;break;case"children":r=u;break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(w(91));break;default:rt(t,e,a,u,n,null)}yd(t,l,i,r),eu(t);return;case"option":for(o in n)if(n.hasOwnProperty(o)&&(l=n[o],l!=null))switch(o){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:rt(t,e,o,l,n,null)}return;case"dialog":F("beforetoggle",t),F("toggle",t),F("cancel",t),F("close",t);break;case"iframe":case"object":F("load",t);break;case"video":case"audio":for(l=0;l<Dr.length;l++)F(Dr[l],t);break;case"image":F("error",t),F("load",t);break;case"details":F("toggle",t);break;case"embed":case"source":case"link":F("error",t),F("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&(l=n[c],l!=null))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(w(137,e));default:rt(t,e,c,l,n,null)}return;default:if(Ss(e)){for(s in n)n.hasOwnProperty(s)&&(l=n[s],l!==void 0&&rs(t,e,s,l,n,void 0));return}}for(u in n)n.hasOwnProperty(u)&&(l=n[u],l!=null&&rt(t,e,u,l,n,null))}function nS(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,r=null,a=null,u=null,o=null,c=null,s=null;for(m in n){var f=n[m];if(n.hasOwnProperty(m)&&f!=null)switch(m){case"checked":break;case"value":break;case"defaultValue":o=f;default:l.hasOwnProperty(m)||rt(t,e,m,null,l,f)}}for(var p in l){var m=l[p];if(f=n[p],l.hasOwnProperty(p)&&(m!=null||f!=null))switch(p){case"type":r=m;break;case"name":i=m;break;case"checked":c=m;break;case"defaultChecked":s=m;break;case"value":a=m;break;case"defaultValue":u=m;break;case"children":case"dangerouslySetInnerHTML":if(m!=null)throw Error(w(137,e));break;default:m!==f&&rt(t,e,p,m,l,f)}}Dc(t,a,u,o,c,s,r,i);return;case"select":m=a=u=p=null;for(r in n)if(o=n[r],n.hasOwnProperty(r)&&o!=null)switch(r){case"value":break;case"multiple":m=o;default:l.hasOwnProperty(r)||rt(t,e,r,null,l,o)}for(i in l)if(r=l[i],o=n[i],l.hasOwnProperty(i)&&(r!=null||o!=null))switch(i){case"value":p=r;break;case"defaultValue":u=r;break;case"multiple":a=r;default:r!==o&&rt(t,e,i,r,l,o)}e=u,n=a,l=m,p!=null?oi(t,!!n,p,!1):!!l!=!!n&&(e!=null?oi(t,!!n,e,!0):oi(t,!!n,n?[]:"",!1));return;case"textarea":m=p=null;for(u in n)if(i=n[u],n.hasOwnProperty(u)&&i!=null&&!l.hasOwnProperty(u))switch(u){case"value":break;case"children":break;default:rt(t,e,u,null,l,i)}for(a in l)if(i=l[a],r=n[a],l.hasOwnProperty(a)&&(i!=null||r!=null))switch(a){case"value":p=i;break;case"defaultValue":m=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(w(91));break;default:i!==r&&rt(t,e,a,i,l,r)}gd(t,p,m);return;case"option":for(var y in n)if(p=n[y],n.hasOwnProperty(y)&&p!=null&&!l.hasOwnProperty(y))switch(y){case"selected":t.selected=!1;break;default:rt(t,e,y,null,l,p)}for(o in l)if(p=l[o],m=n[o],l.hasOwnProperty(o)&&p!==m&&(p!=null||m!=null))switch(o){case"selected":t.selected=p&&typeof p!="function"&&typeof p!="symbol";break;default:rt(t,e,o,p,l,m)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var v in n)p=n[v],n.hasOwnProperty(v)&&p!=null&&!l.hasOwnProperty(v)&&rt(t,e,v,null,l,p);for(c in l)if(p=l[c],m=n[c],l.hasOwnProperty(c)&&p!==m&&(p!=null||m!=null))switch(c){case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(w(137,e));break;default:rt(t,e,c,p,l,m)}return;default:if(Ss(e)){for(var T in n)p=n[T],n.hasOwnProperty(T)&&p!==void 0&&!l.hasOwnProperty(T)&&rs(t,e,T,void 0,l,p);for(s in l)p=l[s],m=n[s],!l.hasOwnProperty(s)||p===m||p===void 0&&m===void 0||rs(t,e,s,p,l,m);return}}for(var h in n)p=n[h],n.hasOwnProperty(h)&&p!=null&&!l.hasOwnProperty(h)&&rt(t,e,h,null,l,p);for(f in l)p=l[f],m=n[f],!l.hasOwnProperty(f)||p===m||p==null&&m==null||rt(t,e,f,p,l,m)}var as=null,us=null;function Su(t){return t.nodeType===9?t:t.ownerDocument}function Bh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function by(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function os(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var bc=null;function lS(){var t=window.event;return t&&t.type==="popstate"?t===bc?!1:(bc=t,!0):(bc=null,!1)}var vy=typeof setTimeout=="function"?setTimeout:void 0,iS=typeof clearTimeout=="function"?clearTimeout:void 0,Hh=typeof Promise=="function"?Promise:void 0,rS=typeof queueMicrotask=="function"?queueMicrotask:typeof Hh!="undefined"?function(t){return Hh.resolve(null).then(t).catch(aS)}:vy;function aS(t){setTimeout(function(){throw t})}function Fn(t){return t==="head"}function qh(t,e){var n=e,l=0,i=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<l&&8>l){n=l;var a=t.ownerDocument;if(n&1&&Sr(a.documentElement),n&2&&Sr(a.body),n&4)for(n=a.head,Sr(n),a=n.firstChild;a;){var u=a.nextSibling,o=a.nodeName;a[jr]||o==="SCRIPT"||o==="STYLE"||o==="LINK"&&a.rel.toLowerCase()==="stylesheet"||n.removeChild(a),a=u}}if(i===0){t.removeChild(r),Lr(e);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=r}while(n);Lr(e)}function cs(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":cs(n),vs(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function uS(t,e,n,l){for(;t.nodeType===1;){var i=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[jr])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==i.rel||t.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||t.getAttribute("title")!==(i.title==null?null:i.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(i.src==null?null:i.src)||t.getAttribute("type")!==(i.type==null?null:i.type)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=i.name==null?null:""+i.name;if(i.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=Le(t.nextSibling),t===null)break}return null}function oS(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Le(t.nextSibling),t===null))return null;return t}function ss(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function cS(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Le(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var fs=null;function jh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function Sy(t,e,n){switch(e=Su(n),t){case"html":if(t=e.documentElement,!t)throw Error(w(452));return t;case"head":if(t=e.head,!t)throw Error(w(453));return t;case"body":if(t=e.body,!t)throw Error(w(454));return t;default:throw Error(w(451))}}function Sr(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);vs(t)}var De=new Map,Yh=new Set;function ku(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var gn=tt.d;tt.d={f:sS,r:fS,D:mS,C:pS,L:hS,m:dS,X:yS,S:gS,M:xS};function sS(){var t=gn.f(),e=Bu();return t||e}function fS(t){var e=zi(t);e!==null&&e.tag===5&&e.type==="form"?hg(e):gn.r(t)}var Mi=typeof document=="undefined"?null:document;function ky(t,e,n){var l=Mi;if(l&&typeof e=="string"&&e){var i=Ae(e);i='link[rel="'+t+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),Yh.has(i)||(Yh.add(i),t={rel:t,crossOrigin:n,href:e},l.querySelector(i)===null&&(e=l.createElement("link"),Qt(e,"link",t),Ut(e),l.head.appendChild(e)))}}function mS(t){gn.D(t),ky("dns-prefetch",t,null)}function pS(t,e){gn.C(t,e),ky("preconnect",t,e)}function hS(t,e,n){gn.L(t,e,n);var l=Mi;if(l&&t&&e){var i='link[rel="preload"][as="'+Ae(e)+'"]';e==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Ae(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Ae(n.imageSizes)+'"]')):i+='[href="'+Ae(t)+'"]';var r=i;switch(e){case"style":r=Ai(t);break;case"script":r=Oi(t)}De.has(r)||(t=st({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),De.set(r,t),l.querySelector(i)!==null||e==="style"&&l.querySelector(Jr(r))||e==="script"&&l.querySelector(Wr(r))||(e=l.createElement("link"),Qt(e,"link",t),Ut(e),l.head.appendChild(e)))}}function dS(t,e){gn.m(t,e);var n=Mi;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",i='link[rel="modulepreload"][as="'+Ae(l)+'"][href="'+Ae(t)+'"]',r=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=Oi(t)}if(!De.has(r)&&(t=st({rel:"modulepreload",href:t},e),De.set(r,t),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Wr(r)))return}l=n.createElement("link"),Qt(l,"link",t),Ut(l),n.head.appendChild(l)}}}function gS(t,e,n){gn.S(t,e,n);var l=Mi;if(l&&t){var i=ui(l).hoistableStyles,r=Ai(t);e=e||"default";var a=i.get(r);if(!a){var u={loading:0,preload:null};if(a=l.querySelector(Jr(r)))u.loading=5;else{t=st({rel:"stylesheet",href:t,"data-precedence":e},n),(n=De.get(r))&&lf(t,n);var o=a=l.createElement("link");Ut(o),Qt(o,"link",t),o._p=new Promise(function(c,s){o.onload=c,o.onerror=s}),o.addEventListener("load",function(){u.loading|=1}),o.addEventListener("error",function(){u.loading|=2}),u.loading|=4,Ka(a,e,l)}a={type:"stylesheet",instance:a,count:1,state:u},i.set(r,a)}}}function yS(t,e){gn.X(t,e);var n=Mi;if(n&&t){var l=ui(n).hoistableScripts,i=Oi(t),r=l.get(i);r||(r=n.querySelector(Wr(i)),r||(t=st({src:t,async:!0},e),(e=De.get(i))&&rf(t,e),r=n.createElement("script"),Ut(r),Qt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function xS(t,e){gn.M(t,e);var n=Mi;if(n&&t){var l=ui(n).hoistableScripts,i=Oi(t),r=l.get(i);r||(r=n.querySelector(Wr(i)),r||(t=st({src:t,async:!0,type:"module"},e),(e=De.get(i))&&rf(t,e),r=n.createElement("script"),Ut(r),Qt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function Vh(t,e,n,l){var i=(i=Ln.current)?ku(i):null;if(!i)throw Error(w(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Ai(n.href),n=ui(i).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Ai(n.href);var r=ui(i).hoistableStyles,a=r.get(t);if(a||(i=i.ownerDocument||i,a={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,a),(r=i.querySelector(Jr(t)))&&!r._p&&(a.instance=r,a.state.loading=5),De.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},De.set(t,n),r||bS(i,t,n,a.state))),e&&l===null)throw Error(w(528,""));return a}if(e&&l!==null)throw Error(w(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Oi(n),n=ui(i).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(w(444,t))}}function Ai(t){return'href="'+Ae(t)+'"'}function Jr(t){return'link[rel="stylesheet"]['+t+"]"}function Ey(t){return st({},t,{"data-precedence":t.precedence,precedence:null})}function bS(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),Qt(e,"link",n),Ut(e),t.head.appendChild(e))}function Oi(t){return'[src="'+Ae(t)+'"]'}function Wr(t){return"script[async]"+t}function Xh(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Ae(n.href)+'"]');if(l)return e.instance=l,Ut(l),l;var i=st({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Ut(l),Qt(l,"style",i),Ka(l,n.precedence,t),e.instance=l;case"stylesheet":i=Ai(n.href);var r=t.querySelector(Jr(i));if(r)return e.state.loading|=4,e.instance=r,Ut(r),r;l=Ey(n),(i=De.get(i))&&lf(l,i),r=(t.ownerDocument||t).createElement("link"),Ut(r);var a=r;return a._p=new Promise(function(u,o){a.onload=u,a.onerror=o}),Qt(r,"link",l),e.state.loading|=4,Ka(r,n.precedence,t),e.instance=r;case"script":return r=Oi(n.src),(i=t.querySelector(Wr(r)))?(e.instance=i,Ut(i),i):(l=n,(i=De.get(r))&&(l=st({},n),rf(l,i)),t=t.ownerDocument||t,i=t.createElement("script"),Ut(i),Qt(i,"link",l),t.head.appendChild(i),e.instance=i);case"void":return null;default:throw Error(w(443,e.type))}else e.type==="stylesheet"&&!(e.state.loading&4)&&(l=e.instance,e.state.loading|=4,Ka(l,n.precedence,t));return e.instance}function Ka(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,r=i,a=0;a<l.length;a++){var u=l[a];if(u.dataset.precedence===e)r=u;else if(r!==i)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function lf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function rf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ia=null;function Gh(t,e,n){if(Ia===null){var l=new Map,i=Ia=new Map;i.set(n,l)}else i=Ia,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),i=0;i<n.length;i++){var r=n[i];if(!(r[jr]||r[Kt]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var a=r.getAttribute(e)||"";a=t+a;var u=l.get(a);u?u.push(r):l.set(a,[r])}}return l}function Qh(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function vS(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Ty(t){return!(t.type==="stylesheet"&&!(t.state.loading&3))}var Or=null;function SS(){}function kS(t,e,n){if(Or===null)throw Error(w(475));var l=Or;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&!(e.state.loading&4)){if(e.instance===null){var i=Ai(n.href),r=t.querySelector(Jr(i));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=Eu.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=r,Ut(r);return}r=t.ownerDocument||t,n=Ey(n),(i=De.get(i))&&lf(n,i),r=r.createElement("link"),Ut(r);var a=r;a._p=new Promise(function(u,o){a.onload=u,a.onerror=o}),Qt(r,"link",n),e.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&!(e.state.loading&3)&&(l.count++,e=Eu.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function ES(){if(Or===null)throw Error(w(475));var t=Or;return t.stylesheets&&t.count===0&&ms(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&ms(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Eu(){if(this.count--,this.count===0){if(this.stylesheets)ms(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Tu=null;function ms(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Tu=new Map,e.forEach(TS,t),Tu=null,Eu.call(t))}function TS(t,e){if(!(e.state.loading&4)){var n=Tu.get(t);if(n)var l=n.get(null);else{n=new Map,Tu.set(t,n);for(var i=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<i.length;r++){var a=i[r];(a.nodeName==="LINK"||a.getAttribute("media")!=="not all")&&(n.set(a.dataset.precedence,a),l=a)}l&&n.set(null,l)}i=e.instance,a=i.getAttribute("data-precedence"),r=n.get(a)||l,r===l&&n.set(null,i),n.set(a,i),this.count++,l=Eu.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),r?r.parentNode.insertBefore(i,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(i,t.firstChild)),e.state.loading|=4}}var _r={$$typeof:rn,Provider:null,Consumer:null,_currentValue:ol,_currentValue2:ol,_threadCount:0};function AS(t,e,n,l,i,r,a,u){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Go(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Go(0),this.hiddenUpdates=Go(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=r,this.onRecoverableError=a,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=u,this.incompleteTransitions=new Map}function Ay(t,e,n,l,i,r,a,u,o,c,s,f){return t=new AS(t,e,n,a,u,o,c,f),e=1,r===!0&&(e|=24),r=he(3,null,null,e),t.current=r,r.stateNode=t,e=_s(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:l,isDehydrated:n,cache:e},Ns(r),t}function wy(t){return t?(t=li,t):li}function zy(t,e,n,l,i,r){i=wy(i),l.context===null?l.context=i:l.pendingContext=i,l=Un(e),l.payload={element:n},r=r===void 0?null:r,r!==null&&(l.callback=r),n=Bn(t,l,e),n!==null&&(xe(n,t,e),pr(n,t,e))}function Zh(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function af(t,e){Zh(t,e),(t=t.alternate)&&Zh(t,e)}function Cy(t){if(t.tag===13){var e=Ci(t,67108864);e!==null&&xe(e,t,67108864),af(t,67108864)}}var Au=!0;function wS(t,e,n,l){var i=H.T;H.T=null;var r=tt.p;try{tt.p=2,uf(t,e,n,l)}finally{tt.p=r,H.T=i}}function zS(t,e,n,l){var i=H.T;H.T=null;var r=tt.p;try{tt.p=8,uf(t,e,n,l)}finally{tt.p=r,H.T=i}}function uf(t,e,n,l){if(Au){var i=ps(l);if(i===null)xc(t,e,l,wu,n),Fh(t,l);else if(DS(i,t,e,n,l))l.stopPropagation();else if(Fh(t,l),e&4&&-1<CS.indexOf(t)){for(;i!==null;){var r=zi(i);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var a=rl(r.pendingLanes);if(a!==0){var u=r;for(u.pendingLanes|=2,u.entangledLanes|=2;a;){var o=1<<31-ge(a);u.entanglements[1]|=o,a&=~o}Ze(r),!(nt&6)&&(gu=Xe()+500,Ir(0,!1))}}break;case 13:u=Ci(r,2),u!==null&&xe(u,r,2),Bu(),af(r,2)}if(r=ps(l),r===null&&xc(t,e,l,wu,n),r===i)break;i=r}i!==null&&l.stopPropagation()}else xc(t,e,l,null,n)}}function ps(t){return t=ks(t),of(t)}var wu=null;function of(t){if(wu=null,t=Wl(t),t!==null){var e=Ur(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=$h(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return wu=t,null}function Dy(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(db()){case ld:return 2;case id:return 8;case tu:case gb:return 32;case rd:return 268435456;default:return 32}default:return 32}}var hs=!1,jn=null,Yn=null,Vn=null,Rr=new Map,Nr=new Map,Mn=[],CS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Fh(t,e){switch(t){case"focusin":case"focusout":jn=null;break;case"dragenter":case"dragleave":Yn=null;break;case"mouseover":case"mouseout":Vn=null;break;case"pointerover":case"pointerout":Rr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Nr.delete(e.pointerId)}}function nr(t,e,n,l,i,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:r,targetContainers:[i]},e!==null&&(e=zi(e),e!==null&&Cy(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function DS(t,e,n,l,i){switch(e){case"focusin":return jn=nr(jn,t,e,n,l,i),!0;case"dragenter":return Yn=nr(Yn,t,e,n,l,i),!0;case"mouseover":return Vn=nr(Vn,t,e,n,l,i),!0;case"pointerover":var r=i.pointerId;return Rr.set(r,nr(Rr.get(r)||null,t,e,n,l,i)),!0;case"gotpointercapture":return r=i.pointerId,Nr.set(r,nr(Nr.get(r)||null,t,e,n,l,i)),!0}return!1}function My(t){var e=Wl(t.target);if(e!==null){var n=Ur(e);if(n!==null){if(e=n.tag,e===13){if(e=$h(n),e!==null){t.blockedOn=e,Tb(t.priority,function(){if(n.tag===13){var l=ye();l=xs(l);var i=Ci(n,l);i!==null&&xe(i,n,l),af(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ja(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=ps(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);Oc=l,n.target.dispatchEvent(l),Oc=null}else return e=zi(n),e!==null&&Cy(e),t.blockedOn=n,!1;e.shift()}return!0}function Kh(t,e,n){Ja(t)&&n.delete(e)}function MS(){hs=!1,jn!==null&&Ja(jn)&&(jn=null),Yn!==null&&Ja(Yn)&&(Yn=null),Vn!==null&&Ja(Vn)&&(Vn=null),Rr.forEach(Kh),Nr.forEach(Kh)}function Ua(t,e){t.blockedOn===e&&(t.blockedOn=null,hs||(hs=!0,_t.unstable_scheduleCallback(_t.unstable_NormalPriority,MS)))}var Ba=null;function Ih(t){Ba!==t&&(Ba=t,_t.unstable_scheduleCallback(_t.unstable_NormalPriority,function(){Ba===t&&(Ba=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],i=t[e+2];if(typeof l!="function"){if(of(l||n)===null)continue;break}var r=zi(n);r!==null&&(t.splice(e,3),e-=3,Fc(r,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Lr(t){function e(o){return Ua(o,t)}jn!==null&&Ua(jn,t),Yn!==null&&Ua(Yn,t),Vn!==null&&Ua(Vn,t),Rr.forEach(e),Nr.forEach(e);for(var n=0;n<Mn.length;n++){var l=Mn[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Mn.length&&(n=Mn[0],n.blockedOn===null);)My(n),n.blockedOn===null&&Mn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],r=n[l+1],a=i[ae]||null;if(typeof r=="function")a||Ih(n);else if(a){var u=null;if(r&&r.hasAttribute("formAction")){if(i=r,a=r[ae]||null)u=a.formAction;else if(of(i)!==null)continue}else u=a.action;typeof u=="function"?n[l+1]=u:(n.splice(l,3),l-=3),Ih(n)}}}function cf(t){this._internalRoot=t}Yu.prototype.render=cf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(w(409));var n=e.current,l=ye();zy(n,l,t,e,null,null)};Yu.prototype.unmount=cf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;zy(t.current,2,null,t,null,null),Bu(),e[wi]=null}};function Yu(t){this._internalRoot=t}Yu.prototype.unstable_scheduleHydration=function(t){if(t){var e=sd();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Mn.length&&e!==0&&e<Mn[n].priority;n++);Mn.splice(n,0,t),n===0&&My(t)}};var Jh=Wh.version;if(Jh!=="19.1.0")throw Error(w(527,Jh,"19.1.0"));tt.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(w(188)):(t=Object.keys(t).join(","),Error(w(268,t)));return t=ob(e),t=t!==null?td(t):null,t=t===null?null:t.stateNode,t};var OS={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:H,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&(lr=__REACT_DEVTOOLS_GLOBAL_HOOK__,!lr.isDisabled&&lr.supportsFiber))try{Br=lr.inject(OS),de=lr}catch(t){}var lr;Vu.createRoot=function(t,e){if(!Ph(t))throw Error(w(299));var n=!1,l="",i=Ag,r=wg,a=zg,u=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(i=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(a=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(u=e.unstable_transitionCallbacks)),e=Ay(t,1,!1,null,null,n,l,i,r,a,u,null),t[wi]=e.current,nf(t),new cf(e)};Vu.hydrateRoot=function(t,e,n){if(!Ph(t))throw Error(w(299));var l=!1,i="",r=Ag,a=wg,u=zg,o=null,c=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(a=n.onCaughtError),n.onRecoverableError!==void 0&&(u=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(o=n.unstable_transitionCallbacks),n.formState!==void 0&&(c=n.formState)),e=Ay(t,1,!0,e,n!=null?n:null,l,i,r,a,u,o,c),e.context=wy(null),n=e.current,l=ye(),l=xs(l),i=Un(l),i.callback=null,Bn(n,i,l),n=l,e.current.lanes=n,qr(e,n),Ze(e),t[wi]=e.current,nf(t),new Yu(e)};Vu.version="19.1.0"});var Ny=Pt((yA,Ry)=>{"use strict";function _y(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(_y)}catch(t){console.error(t)}}_y(),Ry.exports=Oy()});var Ky=Pt((kw,Fy)=>{var Xy=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,YS=/\n/g,VS=/^\s*/,XS=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,GS=/^:\s*/,QS=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,ZS=/^[;\s]*/,FS=/^\s+|\s+$/g,KS=`
`,Gy="/",Qy="*",Al="",IS="comment",JS="declaration";Fy.exports=function(t,e){if(typeof t!="string")throw new TypeError("First argument must be a string");if(!t)return[];e=e||{};var n=1,l=1;function i(v){var T=v.match(YS);T&&(n+=T.length);var h=v.lastIndexOf(KS);l=~h?v.length-h:l+v.length}function r(){var v={line:n,column:l};return function(T){return T.position=new a(v),s(),T}}function a(v){this.start=v,this.end={line:n,column:l},this.source=e.source}a.prototype.content=t;var u=[];function o(v){var T=new Error(e.source+":"+n+":"+l+": "+v);if(T.reason=v,T.filename=e.source,T.line=n,T.column=l,T.source=t,e.silent)u.push(T);else throw T}function c(v){var T=v.exec(t);if(T){var h=T[0];return i(h),t=t.slice(h.length),T}}function s(){c(VS)}function f(v){var T;for(v=v||[];T=p();)T!==!1&&v.push(T);return v}function p(){var v=r();if(!(Gy!=t.charAt(0)||Qy!=t.charAt(1))){for(var T=2;Al!=t.charAt(T)&&(Qy!=t.charAt(T)||Gy!=t.charAt(T+1));)++T;if(T+=2,Al===t.charAt(T-1))return o("End of comment missing");var h=t.slice(2,T-2);return l+=2,i(h),t=t.slice(T),l+=2,v({type:IS,comment:h})}}function m(){var v=r(),T=c(XS);if(T){if(p(),!c(GS))return o("property missing ':'");var h=c(QS),d=v({type:JS,property:Zy(T[0].replace(Xy,Al)),value:h?Zy(h[0].replace(Xy,Al)):Al});return c(ZS),d}}function y(){var v=[];f(v);for(var T;T=m();)T!==!1&&(v.push(T),f(v));return v}return s(),y()};function Zy(t){return t?t.replace(FS,Al):Al}});var Iy=Pt(ta=>{"use strict";var WS=ta&&ta.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(ta,"__esModule",{value:!0});ta.default=$S;var PS=WS(Ky());function $S(t,e){var n=null;if(!t||typeof t!="string")return n;var l=(0,PS.default)(t),i=typeof e=="function";return l.forEach(function(r){if(r.type==="declaration"){var a=r.property,u=r.value;i?e(a,u,r):u&&(n=n||{},n[a]=u)}}),n}});var Wy=Pt(Fu=>{"use strict";Object.defineProperty(Fu,"__esModule",{value:!0});Fu.camelCase=void 0;var tk=/^--[a-zA-Z0-9_-]+$/,ek=/-([a-z])/g,nk=/^[^-]+$/,lk=/^-(webkit|moz|ms|o|khtml)-/,ik=/^-(ms)-/,rk=function(t){return!t||nk.test(t)||tk.test(t)},ak=function(t,e){return e.toUpperCase()},Jy=function(t,e){return"".concat(e,"-")},uk=function(t,e){return e===void 0&&(e={}),rk(t)?t:(t=t.toLowerCase(),e.reactCompat?t=t.replace(ik,Jy):t=t.replace(lk,Jy),t.replace(ek,ak))};Fu.camelCase=uk});var $y=Pt((Sf,Py)=>{"use strict";var ok=Sf&&Sf.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},ck=ok(Iy()),sk=Wy();function vf(t,e){var n={};return!t||typeof t!="string"||(0,ck.default)(t,function(l,i){l&&i&&(n[(0,sk.camelCase)(l,e)]=i)}),n}vf.default=vf;Py.exports=vf});var c1=Pt(Iu=>{"use strict";var Mk=Symbol.for("react.transitional.element"),Ok=Symbol.for("react.fragment");function o1(t,e,n){var l=null;if(n!==void 0&&(l=""+n),e.key!==void 0&&(l=""+e.key),"key"in e){n={};for(var i in e)i!=="key"&&(n[i]=e[i])}else n=e;return e=n.ref,{$$typeof:Mk,type:t,key:l,ref:e!==void 0?e:null,props:n}}Iu.Fragment=Ok;Iu.jsx=o1;Iu.jsxs=o1});var Ju=Pt((Jw,s1)=>{"use strict";s1.exports=c1()});var w0=Pt((fM,A0)=>{"use strict";var vo=Object.prototype.hasOwnProperty,T0=Object.prototype.toString,x0=Object.defineProperty,b0=Object.getOwnPropertyDescriptor,v0=function(e){return typeof Array.isArray=="function"?Array.isArray(e):T0.call(e)==="[object Array]"},S0=function(e){if(!e||T0.call(e)!=="[object Object]")return!1;var n=vo.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&vo.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!n&&!l)return!1;var i;for(i in e);return typeof i=="undefined"||vo.call(e,i)},k0=function(e,n){x0&&n.name==="__proto__"?x0(e,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):e[n.name]=n.newValue},E0=function(e,n){if(n==="__proto__")if(vo.call(e,n)){if(b0)return b0(e,n).value}else return;return e[n]};A0.exports=function t(){var e,n,l,i,r,a,u=arguments[0],o=1,c=arguments.length,s=!1;for(typeof u=="boolean"&&(s=u,u=arguments[1]||{},o=2),(u==null||typeof u!="object"&&typeof u!="function")&&(u={});o<c;++o)if(e=arguments[o],e!=null)for(n in e)l=E0(u,n),i=E0(e,n),u!==i&&(s&&i&&(S0(i)||(r=v0(i)))?(r?(r=!1,a=l&&v0(l)?l:[]):a=l&&S0(l)?l:{},k0(u,{name:n,newValue:t(s,a,i)})):typeof i!="undefined"&&k0(u,{name:n,newValue:i}));return u}});var Zt=vn(Qi()),wx=vn(Ny());function Ly(t,e){let n=e||{};return(t[t.length-1]===""?[...t,""]:t).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}var _S=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,RS=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,NS={};function Xu(t,e){return((e||NS).jsx?RS:_S).test(t)}var LS=/[ \t\n\f\r]/g;function sf(t){return typeof t=="object"?t.type==="text"?Uy(t.value):!1:Uy(t)}function Uy(t){return t.replace(LS,"")===""}var yn=class{constructor(e,n,l){this.normal=n,this.property=e,l&&(this.space=l)}};yn.prototype.normal={};yn.prototype.property={};yn.prototype.space=void 0;function ff(t,e){let n={},l={};for(let i of t)Object.assign(n,i.property),Object.assign(l,i.normal);return new yn(n,l,e)}function Pr(t){return t.toLowerCase()}var qt=class{constructor(e,n){this.attribute=n,this.property=e}};qt.prototype.attribute="";qt.prototype.booleanish=!1;qt.prototype.boolean=!1;qt.prototype.commaOrSpaceSeparated=!1;qt.prototype.commaSeparated=!1;qt.prototype.defined=!1;qt.prototype.mustUseProperty=!1;qt.prototype.number=!1;qt.prototype.overloadedBoolean=!1;qt.prototype.property="";qt.prototype.spaceSeparated=!1;qt.prototype.space=void 0;var $r={};$m($r,{boolean:()=>Y,booleanish:()=>vt,commaOrSpaceSeparated:()=>oe,commaSeparated:()=>Kn,number:()=>D,overloadedBoolean:()=>mf,spaceSeparated:()=>lt});var US=0,Y=El(),vt=El(),mf=El(),D=El(),lt=El(),Kn=El(),oe=El();function El(){return Wm(2,++US)}var pf=Object.keys($r),Tl=class extends qt{constructor(e,n,l,i){let r=-1;if(super(e,n),By(this,"space",i),typeof l=="number")for(;++r<pf.length;){let a=pf[r];By(this,pf[r],(l&$r[a])===$r[a])}}};Tl.prototype.defined=!0;function By(t,e,n){n&&(t[e]=n)}function Me(t){let e={},n={};for(let[l,i]of Object.entries(t.properties)){let r=new Tl(l,t.transform(t.attributes||{},l),i,t.space);t.mustUseProperty&&t.mustUseProperty.includes(l)&&(r.mustUseProperty=!0),e[l]=r,n[Pr(l)]=l,n[Pr(r.attribute)]=l}return new yn(e,n,t.space)}var hf=Me({properties:{ariaActiveDescendant:null,ariaAtomic:vt,ariaAutoComplete:null,ariaBusy:vt,ariaChecked:vt,ariaColCount:D,ariaColIndex:D,ariaColSpan:D,ariaControls:lt,ariaCurrent:null,ariaDescribedBy:lt,ariaDetails:null,ariaDisabled:vt,ariaDropEffect:lt,ariaErrorMessage:null,ariaExpanded:vt,ariaFlowTo:lt,ariaGrabbed:vt,ariaHasPopup:null,ariaHidden:vt,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:lt,ariaLevel:D,ariaLive:null,ariaModal:vt,ariaMultiLine:vt,ariaMultiSelectable:vt,ariaOrientation:null,ariaOwns:lt,ariaPlaceholder:null,ariaPosInSet:D,ariaPressed:vt,ariaReadOnly:vt,ariaRelevant:null,ariaRequired:vt,ariaRoleDescription:lt,ariaRowCount:D,ariaRowIndex:D,ariaRowSpan:D,ariaSelected:vt,ariaSetSize:D,ariaSort:null,ariaValueMax:D,ariaValueMin:D,ariaValueNow:D,ariaValueText:null,role:null},transform(t,e){return e==="role"?e:"aria-"+e.slice(4).toLowerCase()}});function Gu(t,e){return e in t?t[e]:e}function Qu(t,e){return Gu(t,e.toLowerCase())}var Hy=Me({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Kn,acceptCharset:lt,accessKey:lt,action:null,allow:null,allowFullScreen:Y,allowPaymentRequest:Y,allowUserMedia:Y,alt:null,as:null,async:Y,autoCapitalize:null,autoComplete:lt,autoFocus:Y,autoPlay:Y,blocking:lt,capture:null,charSet:null,checked:Y,cite:null,className:lt,cols:D,colSpan:null,content:null,contentEditable:vt,controls:Y,controlsList:lt,coords:D|Kn,crossOrigin:null,data:null,dateTime:null,decoding:null,default:Y,defer:Y,dir:null,dirName:null,disabled:Y,download:mf,draggable:vt,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:Y,formTarget:null,headers:lt,height:D,hidden:Y,high:D,href:null,hrefLang:null,htmlFor:lt,httpEquiv:lt,id:null,imageSizes:null,imageSrcSet:null,inert:Y,inputMode:null,integrity:null,is:null,isMap:Y,itemId:null,itemProp:lt,itemRef:lt,itemScope:Y,itemType:lt,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:Y,low:D,manifest:null,max:null,maxLength:D,media:null,method:null,min:null,minLength:D,multiple:Y,muted:Y,name:null,nonce:null,noModule:Y,noValidate:Y,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:Y,optimum:D,pattern:null,ping:lt,placeholder:null,playsInline:Y,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:Y,referrerPolicy:null,rel:lt,required:Y,reversed:Y,rows:D,rowSpan:D,sandbox:lt,scope:null,scoped:Y,seamless:Y,selected:Y,shadowRootClonable:Y,shadowRootDelegatesFocus:Y,shadowRootMode:null,shape:null,size:D,sizes:null,slot:null,span:D,spellCheck:vt,src:null,srcDoc:null,srcLang:null,srcSet:null,start:D,step:null,style:null,tabIndex:D,target:null,title:null,translate:null,type:null,typeMustMatch:Y,useMap:null,value:vt,width:D,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:lt,axis:null,background:null,bgColor:null,border:D,borderColor:null,bottomMargin:D,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:Y,declare:Y,event:null,face:null,frame:null,frameBorder:null,hSpace:D,leftMargin:D,link:null,longDesc:null,lowSrc:null,marginHeight:D,marginWidth:D,noResize:Y,noHref:Y,noShade:Y,noWrap:Y,object:null,profile:null,prompt:null,rev:null,rightMargin:D,rules:null,scheme:null,scrolling:vt,standby:null,summary:null,text:null,topMargin:D,valueType:null,version:null,vAlign:null,vLink:null,vSpace:D,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:Y,disableRemotePlayback:Y,prefix:null,property:null,results:D,security:null,unselectable:null},space:"html",transform:Qu});var qy=Me({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:oe,accentHeight:D,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:D,amplitude:D,arabicForm:null,ascent:D,attributeName:null,attributeType:null,azimuth:D,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:D,by:null,calcMode:null,capHeight:D,className:lt,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:D,diffuseConstant:D,direction:null,display:null,dur:null,divisor:D,dominantBaseline:null,download:Y,dx:null,dy:null,edgeMode:null,editable:null,elevation:D,enableBackground:null,end:null,event:null,exponent:D,externalResourcesRequired:null,fill:null,fillOpacity:D,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Kn,g2:Kn,glyphName:Kn,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:D,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:D,horizOriginX:D,horizOriginY:D,id:null,ideographic:D,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:D,k:D,k1:D,k2:D,k3:D,k4:D,kernelMatrix:oe,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:D,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:D,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:D,overlineThickness:D,paintOrder:null,panose1:null,path:null,pathLength:D,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:lt,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:D,pointsAtY:D,pointsAtZ:D,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:oe,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:oe,rev:oe,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:oe,requiredFeatures:oe,requiredFonts:oe,requiredFormats:oe,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:D,specularExponent:D,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:D,strikethroughThickness:D,string:null,stroke:null,strokeDashArray:oe,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:D,strokeOpacity:D,strokeWidth:null,style:null,surfaceScale:D,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:oe,tabIndex:D,tableValues:null,target:null,targetX:D,targetY:D,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:oe,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:D,underlineThickness:D,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:D,values:null,vAlphabetic:D,vMathematical:D,vectorEffect:null,vHanging:D,vIdeographic:D,version:null,vertAdvY:D,vertOriginX:D,vertOriginY:D,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:D,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Gu});var df=Me({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(t,e){return"xlink:"+e.slice(5).toLowerCase()}});var gf=Me({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Qu});var yf=Me({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(t,e){return"xml:"+e.slice(3).toLowerCase()}});var xf={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var BS=/[A-Z]/g,jy=/-[a-z]/g,HS=/^data[-\w.:]+$/i;function bf(t,e){let n=Pr(e),l=e,i=qt;if(n in t.normal)return t.property[t.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&HS.test(e)){if(e.charAt(4)==="-"){let r=e.slice(5).replace(jy,jS);l="data"+r.charAt(0).toUpperCase()+r.slice(1)}else{let r=e.slice(4);if(!jy.test(r)){let a=r.replace(BS,qS);a.charAt(0)!=="-"&&(a="-"+a),e="data"+a}}i=Tl}return new i(l,e)}function qS(t){return"-"+t.toLowerCase()}function jS(t){return t.charAt(1).toUpperCase()}var Yy=ff([hf,Hy,df,gf,yf],"html"),Zu=ff([hf,qy,df,gf,yf],"svg");function Vy(t){return t.join(" ").trim()}var l1=vn($y(),1);var Ku=t1("end"),_i=t1("start");function t1(t){return e;function e(n){let l=n&&n.position&&n.position[t]||{};if(typeof l.line=="number"&&l.line>0&&typeof l.column=="number"&&l.column>0)return{line:l.line,column:l.column,offset:typeof l.offset=="number"&&l.offset>-1?l.offset:void 0}}}function kf(t){let e=_i(t),n=Ku(t);if(e&&n)return{start:e,end:n}}function In(t){return!t||typeof t!="object"?"":"position"in t||"type"in t?e1(t.position):"start"in t||"end"in t?e1(t):"line"in t||"column"in t?Ef(t):""}function Ef(t){return n1(t&&t.line)+":"+n1(t&&t.column)}function e1(t){return Ef(t&&t.start)+"-"+Ef(t&&t.end)}function n1(t){return t&&typeof t=="number"?t:1}var Tt=class extends Error{constructor(e,n,l){super(),typeof n=="string"&&(l=n,n=void 0);let i="",r={},a=!1;if(n&&("line"in n&&"column"in n?r={place:n}:"start"in n&&"end"in n?r={place:n}:"type"in n?r={ancestors:[n],place:n.position}:r=C({},n)),typeof e=="string"?i=e:!r.cause&&e&&(a=!0,i=e.message,r.cause=e),!r.ruleId&&!r.source&&typeof l=="string"){let o=l.indexOf(":");o===-1?r.ruleId=l:(r.source=l.slice(0,o),r.ruleId=l.slice(o+1))}if(!r.place&&r.ancestors&&r.ancestors){let o=r.ancestors[r.ancestors.length-1];o&&(r.place=o.position)}let u=r.place&&"start"in r.place?r.place.start:r.place;this.ancestors=r.ancestors||void 0,this.cause=r.cause||void 0,this.column=u?u.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=u?u.line:void 0,this.name=In(r.place)||"1:1",this.place=r.place||void 0,this.reason=this.message,this.ruleId=r.ruleId||void 0,this.source=r.source||void 0,this.stack=a&&r.cause&&typeof r.cause.stack=="string"?r.cause.stack:"",this.actual,this.expected,this.note,this.url}};Tt.prototype.file="";Tt.prototype.name="";Tt.prototype.reason="";Tt.prototype.message="";Tt.prototype.stack="";Tt.prototype.column=void 0;Tt.prototype.line=void 0;Tt.prototype.ancestors=void 0;Tt.prototype.cause=void 0;Tt.prototype.fatal=void 0;Tt.prototype.place=void 0;Tt.prototype.ruleId=void 0;Tt.prototype.source=void 0;var Tf={}.hasOwnProperty,fk=new Map,mk=/[A-Z]/g,pk=new Set(["table","tbody","thead","tfoot","tr"]),hk=new Set(["td","th"]),i1="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function Af(t,e){if(!e||e.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");let n=e.filePath||void 0,l;if(e.development){if(typeof e.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");l=kk(n,e.jsxDEV)}else{if(typeof e.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof e.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");l=Sk(n,e.jsx,e.jsxs)}let i={Fragment:e.Fragment,ancestors:[],components:e.components||{},create:l,elementAttributeNameCase:e.elementAttributeNameCase||"react",evaluater:e.createEvaluater?e.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:e.ignoreInvalidStyle||!1,passKeys:e.passKeys!==!1,passNode:e.passNode||!1,schema:e.space==="svg"?Zu:Yy,stylePropertyNameCase:e.stylePropertyNameCase||"dom",tableCellAlignToStyle:e.tableCellAlignToStyle!==!1},r=r1(i,t,void 0);return r&&typeof r!="string"?r:i.create(t,i.Fragment,{children:r||void 0},void 0)}function r1(t,e,n){if(e.type==="element")return dk(t,e,n);if(e.type==="mdxFlowExpression"||e.type==="mdxTextExpression")return gk(t,e);if(e.type==="mdxJsxFlowElement"||e.type==="mdxJsxTextElement")return xk(t,e,n);if(e.type==="mdxjsEsm")return yk(t,e);if(e.type==="root")return bk(t,e,n);if(e.type==="text")return vk(t,e)}function dk(t,e,n){let l=t.schema,i=l;e.tagName.toLowerCase()==="svg"&&l.space==="html"&&(i=Zu,t.schema=i),t.ancestors.push(e);let r=u1(t,e.tagName,!1),a=Ek(t,e),u=zf(t,e);return pk.has(e.tagName)&&(u=u.filter(function(o){return typeof o=="string"?!sf(o):!0})),a1(t,a,r,e),wf(a,u),t.ancestors.pop(),t.schema=l,t.create(e,r,a,n)}function gk(t,e){if(e.data&&e.data.estree&&t.evaluater){let l=e.data.estree.body[0];return l.type,t.evaluater.evaluateExpression(l.expression)}ea(t,e.position)}function yk(t,e){if(e.data&&e.data.estree&&t.evaluater)return t.evaluater.evaluateProgram(e.data.estree);ea(t,e.position)}function xk(t,e,n){let l=t.schema,i=l;e.name==="svg"&&l.space==="html"&&(i=Zu,t.schema=i),t.ancestors.push(e);let r=e.name===null?t.Fragment:u1(t,e.name,!0),a=Tk(t,e),u=zf(t,e);return a1(t,a,r,e),wf(a,u),t.ancestors.pop(),t.schema=l,t.create(e,r,a,n)}function bk(t,e,n){let l={};return wf(l,zf(t,e)),t.create(e,t.Fragment,l,n)}function vk(t,e){return e.value}function a1(t,e,n,l){typeof n!="string"&&n!==t.Fragment&&t.passNode&&(e.node=l)}function wf(t,e){if(e.length>0){let n=e.length>1?e:e[0];n&&(t.children=n)}}function Sk(t,e,n){return l;function l(i,r,a,u){let c=Array.isArray(a.children)?n:e;return u?c(r,a,u):c(r,a)}}function kk(t,e){return n;function n(l,i,r,a){let u=Array.isArray(r.children),o=_i(l);return e(i,r,a,u,{columnNumber:o?o.column-1:void 0,fileName:t,lineNumber:o?o.line:void 0},void 0)}}function Ek(t,e){let n={},l,i;for(i in e.properties)if(i!=="children"&&Tf.call(e.properties,i)){let r=Ak(t,i,e.properties[i]);if(r){let[a,u]=r;t.tableCellAlignToStyle&&a==="align"&&typeof u=="string"&&hk.has(e.tagName)?l=u:n[a]=u}}if(l){let r=n.style||(n.style={});r[t.stylePropertyNameCase==="css"?"text-align":"textAlign"]=l}return n}function Tk(t,e){let n={};for(let l of e.attributes)if(l.type==="mdxJsxExpressionAttribute")if(l.data&&l.data.estree&&t.evaluater){let r=l.data.estree.body[0];r.type;let a=r.expression;a.type;let u=a.properties[0];u.type,Object.assign(n,t.evaluater.evaluateExpression(u.argument))}else ea(t,e.position);else{let i=l.name,r;if(l.value&&typeof l.value=="object")if(l.value.data&&l.value.data.estree&&t.evaluater){let u=l.value.data.estree.body[0];u.type,r=t.evaluater.evaluateExpression(u.expression)}else ea(t,e.position);else r=l.value===null?!0:l.value;n[i]=r}return n}function zf(t,e){let n=[],l=-1,i=t.passKeys?new Map:fk;for(;++l<e.children.length;){let r=e.children[l],a;if(t.passKeys){let o=r.type==="element"?r.tagName:r.type==="mdxJsxFlowElement"||r.type==="mdxJsxTextElement"?r.name:void 0;if(o){let c=i.get(o)||0;a=o+"-"+c,i.set(o,c+1)}}let u=r1(t,r,a);u!==void 0&&n.push(u)}return n}function Ak(t,e,n){let l=bf(t.schema,e);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=l.commaSeparated?Ly(n):Vy(n)),l.property==="style"){let i=typeof n=="object"?n:wk(t,String(n));return t.stylePropertyNameCase==="css"&&(i=zk(i)),["style",i]}return[t.elementAttributeNameCase==="react"&&l.space?xf[l.property]||l.property:l.attribute,n]}}function wk(t,e){try{return(0,l1.default)(e,{reactCompat:!0})}catch(n){if(t.ignoreInvalidStyle)return{};let l=n,i=new Tt("Cannot parse `style` attribute",{ancestors:t.ancestors,cause:l,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw i.file=t.filePath||void 0,i.url=i1+"#cannot-parse-style-attribute",i}}function u1(t,e,n){let l;if(!n)l={type:"Literal",value:e};else if(e.includes(".")){let i=e.split("."),r=-1,a;for(;++r<i.length;){let u=Xu(i[r])?{type:"Identifier",name:i[r]}:{type:"Literal",value:i[r]};a=a?{type:"MemberExpression",object:a,property:u,computed:!!(r&&u.type==="Literal"),optional:!1}:u}l=a}else l=Xu(e)&&!/^[a-z]/.test(e)?{type:"Identifier",name:e}:{type:"Literal",value:e};if(l.type==="Literal"){let i=l.value;return Tf.call(t.components,i)?t.components[i]:i}if(t.evaluater)return t.evaluater.evaluateExpression(l);ea(t)}function ea(t,e){let n=new Tt("Cannot handle MDX estrees without `createEvaluater`",{ancestors:t.ancestors,place:e,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=t.filePath||void 0,n.url=i1+"#cannot-handle-mdx-estrees-without-createevaluater",n}function zk(t){let e={},n;for(n in t)Tf.call(t,n)&&(e[Ck(n)]=t[n]);return e}function Ck(t){let e=t.replace(mk,Dk);return e.slice(0,3)==="ms-"&&(e="-"+e),e}function Dk(t){return"-"+t.toLowerCase()}var na={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var Bi=vn(Ju(),1),U0=vn(Qi(),1);var _k={};function wl(t,e){let n=e||_k,l=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return m1(t,l,i)}function m1(t,e,n){if(Rk(t)){if("value"in t)return t.type==="html"&&!n?"":t.value;if(e&&"alt"in t&&t.alt)return t.alt;if("children"in t)return f1(t.children,e,n)}return Array.isArray(t)?f1(t,e,n):""}function f1(t,e,n){let l=[],i=-1;for(;++i<t.length;)l[i]=m1(t[i],e,n);return l.join("")}function Rk(t){return!!(t&&typeof t=="object")}var p1=document.createElement("i");function Ri(t){let e="&"+t+";";p1.innerHTML=e;let n=p1.textContent;return n.charCodeAt(n.length-1)===59&&t!=="semi"||n===e?!1:n}function At(t,e,n,l){let i=t.length,r=0,a;if(e<0?e=-e>i?0:i+e:e=e>i?i:e,n=n>0?n:0,l.length<1e4)a=Array.from(l),a.unshift(e,n),t.splice(...a);else for(n&&t.splice(e,n);r<l.length;)a=l.slice(r,r+1e4),a.unshift(e,0),t.splice(...a),r+=1e4,e+=1e4}function ne(t,e){return t.length>0?(At(t,t.length,0,e),t):e}var h1={}.hasOwnProperty;function Wu(t){let e={},n=-1;for(;++n<t.length;)Nk(e,t[n]);return e}function Nk(t,e){let n;for(n in e){let i=(h1.call(t,n)?t[n]:void 0)||(t[n]={}),r=e[n],a;if(r)for(a in r){h1.call(i,a)||(i[a]=[]);let u=r[a];Lk(i[a],Array.isArray(u)?u:u?[u]:[])}}}function Lk(t,e){let n=-1,l=[];for(;++n<e.length;)(e[n].add==="after"?t:l).push(e[n]);At(t,0,0,l)}function Pu(t,e){let n=Number.parseInt(t,e);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"\uFFFD":String.fromCodePoint(n)}function Jt(t){return t.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}var Rt=Jn(/[A-Za-z]/),St=Jn(/[\dA-Za-z]/),d1=Jn(/[#-'*+\--9=?A-Z^-~]/);function zl(t){return t!==null&&(t<32||t===127)}var la=Jn(/\d/),g1=Jn(/[\dA-Fa-f]/),y1=Jn(/[!-/:-@[-`{-~]/);function R(t){return t!==null&&t<-2}function Q(t){return t!==null&&(t<0||t===32)}function B(t){return t===-2||t===-1||t===32}var Cl=Jn(new RegExp("\\p{P}|\\p{S}","u")),Fe=Jn(/\s/);function Jn(t){return e;function e(n){return n!==null&&n>-1&&t.test(String.fromCharCode(n))}}function Oe(t){let e=[],n=-1,l=0,i=0;for(;++n<t.length;){let r=t.charCodeAt(n),a="";if(r===37&&St(t.charCodeAt(n+1))&&St(t.charCodeAt(n+2)))i=2;else if(r<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(r))||(a=String.fromCharCode(r));else if(r>55295&&r<57344){let u=t.charCodeAt(n+1);r<56320&&u>56319&&u<57344?(a=String.fromCharCode(r,u),i=1):a="\uFFFD"}else a=String.fromCharCode(r);a&&(e.push(t.slice(l,n),encodeURIComponent(a)),l=n+i+1,a=""),i&&(n+=i,i=0)}return e.join("")+t.slice(l)}function U(t,e,n,l){let i=l?l-1:Number.POSITIVE_INFINITY,r=0;return a;function a(o){return B(o)?(t.enter(n),u(o)):e(o)}function u(o){return B(o)&&r++<i?(t.consume(o),u):(t.exit(n),e(o))}}var x1={tokenize:Uk};function Uk(t){let e=t.attempt(this.parser.constructs.contentInitial,l,i),n;return e;function l(u){if(u===null){t.consume(u);return}return t.enter("lineEnding"),t.consume(u),t.exit("lineEnding"),U(t,e,"linePrefix")}function i(u){return t.enter("paragraph"),r(u)}function r(u){let o=t.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=o),n=o,a(u)}function a(u){if(u===null){t.exit("chunkText"),t.exit("paragraph"),t.consume(u);return}return R(u)?(t.consume(u),t.exit("chunkText"),r):(t.consume(u),a)}}var v1={tokenize:Bk},b1={tokenize:Hk};function Bk(t){let e=this,n=[],l=0,i,r,a;return u;function u(g){if(l<n.length){let k=n[l];return e.containerState=k[1],t.attempt(k[0].continuation,o,c)(g)}return c(g)}function o(g){if(l++,e.containerState._closeFlow){e.containerState._closeFlow=void 0,i&&d();let k=e.events.length,z=k,E;for(;z--;)if(e.events[z][0]==="exit"&&e.events[z][1].type==="chunkFlow"){E=e.events[z][1].end;break}h(l);let M=k;for(;M<e.events.length;)e.events[M][1].end=C({},E),M++;return At(e.events,z+1,0,e.events.slice(k)),e.events.length=M,c(g)}return u(g)}function c(g){if(l===n.length){if(!i)return p(g);if(i.currentConstruct&&i.currentConstruct.concrete)return y(g);e.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return e.containerState={},t.check(b1,s,f)(g)}function s(g){return i&&d(),h(l),p(g)}function f(g){return e.parser.lazy[e.now().line]=l!==n.length,a=e.now().offset,y(g)}function p(g){return e.containerState={},t.attempt(b1,m,y)(g)}function m(g){return l++,n.push([e.currentConstruct,e.containerState]),p(g)}function y(g){if(g===null){i&&d(),h(0),t.consume(g);return}return i=i||e.parser.flow(e.now()),t.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:r}),v(g)}function v(g){if(g===null){T(t.exit("chunkFlow"),!0),h(0),t.consume(g);return}return R(g)?(t.consume(g),T(t.exit("chunkFlow")),l=0,e.interrupt=void 0,u):(t.consume(g),v)}function T(g,k){let z=e.sliceStream(g);if(k&&z.push(null),g.previous=r,r&&(r.next=g),r=g,i.defineSkip(g.start),i.write(z),e.parser.lazy[g.start.line]){let E=i.events.length;for(;E--;)if(i.events[E][1].start.offset<a&&(!i.events[E][1].end||i.events[E][1].end.offset>a))return;let M=e.events.length,O=M,L,S;for(;O--;)if(e.events[O][0]==="exit"&&e.events[O][1].type==="chunkFlow"){if(L){S=e.events[O][1].end;break}L=!0}for(h(l),E=M;E<e.events.length;)e.events[E][1].end=C({},S),E++;At(e.events,O+1,0,e.events.slice(M)),e.events.length=E}}function h(g){let k=n.length;for(;k-- >g;){let z=n[k];e.containerState=z[1],z[0].exit.call(e,t)}n.length=g}function d(){i.write([null]),r=void 0,i=void 0,e.containerState._closeFlow=void 0}}function Hk(t,e,n){return U(t,t.attempt(this.parser.constructs.document,e,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function xn(t){if(t===null||Q(t)||Fe(t))return 1;if(Cl(t))return 2}function Wn(t,e,n){let l=[],i=-1;for(;++i<t.length;){let r=t[i].resolveAll;r&&!l.includes(r)&&(e=r(e,n),l.push(r))}return e}var ia={name:"attention",resolveAll:qk,tokenize:jk};function qk(t,e){let n=-1,l,i,r,a,u,o,c,s;for(;++n<t.length;)if(t[n][0]==="enter"&&t[n][1].type==="attentionSequence"&&t[n][1]._close){for(l=n;l--;)if(t[l][0]==="exit"&&t[l][1].type==="attentionSequence"&&t[l][1]._open&&e.sliceSerialize(t[l][1]).charCodeAt(0)===e.sliceSerialize(t[n][1]).charCodeAt(0)){if((t[l][1]._close||t[n][1]._open)&&(t[n][1].end.offset-t[n][1].start.offset)%3&&!((t[l][1].end.offset-t[l][1].start.offset+t[n][1].end.offset-t[n][1].start.offset)%3))continue;o=t[l][1].end.offset-t[l][1].start.offset>1&&t[n][1].end.offset-t[n][1].start.offset>1?2:1;let f=C({},t[l][1].end),p=C({},t[n][1].start);S1(f,-o),S1(p,o),a={type:o>1?"strongSequence":"emphasisSequence",start:f,end:C({},t[l][1].end)},u={type:o>1?"strongSequence":"emphasisSequence",start:C({},t[n][1].start),end:p},r={type:o>1?"strongText":"emphasisText",start:C({},t[l][1].end),end:C({},t[n][1].start)},i={type:o>1?"strong":"emphasis",start:C({},a.start),end:C({},u.end)},t[l][1].end=C({},a.start),t[n][1].start=C({},u.end),c=[],t[l][1].end.offset-t[l][1].start.offset&&(c=ne(c,[["enter",t[l][1],e],["exit",t[l][1],e]])),c=ne(c,[["enter",i,e],["enter",a,e],["exit",a,e],["enter",r,e]]),c=ne(c,Wn(e.parser.constructs.insideSpan.null,t.slice(l+1,n),e)),c=ne(c,[["exit",r,e],["enter",u,e],["exit",u,e],["exit",i,e]]),t[n][1].end.offset-t[n][1].start.offset?(s=2,c=ne(c,[["enter",t[n][1],e],["exit",t[n][1],e]])):s=0,At(t,l-1,n-l+3,c),n=l+c.length-s-2;break}}for(n=-1;++n<t.length;)t[n][1].type==="attentionSequence"&&(t[n][1].type="data");return t}function jk(t,e){let n=this.parser.constructs.attentionMarkers.null,l=this.previous,i=xn(l),r;return a;function a(o){return r=o,t.enter("attentionSequence"),u(o)}function u(o){if(o===r)return t.consume(o),u;let c=t.exit("attentionSequence"),s=xn(o),f=!s||s===2&&i||n.includes(o),p=!i||i===2&&s||n.includes(l);return c._open=!!(r===42?f:f&&(i||!p)),c._close=!!(r===42?p:p&&(s||!f)),e(o)}}function S1(t,e){t.column+=e,t.offset+=e,t._bufferIndex+=e}var Cf={name:"autolink",tokenize:Yk};function Yk(t,e,n){let l=0;return i;function i(m){return t.enter("autolink"),t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.enter("autolinkProtocol"),r}function r(m){return Rt(m)?(t.consume(m),a):m===64?n(m):c(m)}function a(m){return m===43||m===45||m===46||St(m)?(l=1,u(m)):c(m)}function u(m){return m===58?(t.consume(m),l=0,o):(m===43||m===45||m===46||St(m))&&l++<32?(t.consume(m),u):(l=0,c(m))}function o(m){return m===62?(t.exit("autolinkProtocol"),t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.exit("autolink"),e):m===null||m===32||m===60||zl(m)?n(m):(t.consume(m),o)}function c(m){return m===64?(t.consume(m),s):d1(m)?(t.consume(m),c):n(m)}function s(m){return St(m)?f(m):n(m)}function f(m){return m===46?(t.consume(m),l=0,s):m===62?(t.exit("autolinkProtocol").type="autolinkEmail",t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.exit("autolink"),e):p(m)}function p(m){if((m===45||St(m))&&l++<63){let y=m===45?p:f;return t.consume(m),y}return n(m)}}var Ke={partial:!0,tokenize:Vk};function Vk(t,e,n){return l;function l(r){return B(r)?U(t,i,"linePrefix")(r):i(r)}function i(r){return r===null||R(r)?e(r):n(r)}}var $u={continuation:{tokenize:Gk},exit:Qk,name:"blockQuote",tokenize:Xk};function Xk(t,e,n){let l=this;return i;function i(a){if(a===62){let u=l.containerState;return u.open||(t.enter("blockQuote",{_container:!0}),u.open=!0),t.enter("blockQuotePrefix"),t.enter("blockQuoteMarker"),t.consume(a),t.exit("blockQuoteMarker"),r}return n(a)}function r(a){return B(a)?(t.enter("blockQuotePrefixWhitespace"),t.consume(a),t.exit("blockQuotePrefixWhitespace"),t.exit("blockQuotePrefix"),e):(t.exit("blockQuotePrefix"),e(a))}}function Gk(t,e,n){let l=this;return i;function i(a){return B(a)?U(t,r,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a):r(a)}function r(a){return t.attempt($u,e,n)(a)}}function Qk(t){t.exit("blockQuote")}var to={name:"characterEscape",tokenize:Zk};function Zk(t,e,n){return l;function l(r){return t.enter("characterEscape"),t.enter("escapeMarker"),t.consume(r),t.exit("escapeMarker"),i}function i(r){return y1(r)?(t.enter("characterEscapeValue"),t.consume(r),t.exit("characterEscapeValue"),t.exit("characterEscape"),e):n(r)}}var eo={name:"characterReference",tokenize:Fk};function Fk(t,e,n){let l=this,i=0,r,a;return u;function u(f){return t.enter("characterReference"),t.enter("characterReferenceMarker"),t.consume(f),t.exit("characterReferenceMarker"),o}function o(f){return f===35?(t.enter("characterReferenceMarkerNumeric"),t.consume(f),t.exit("characterReferenceMarkerNumeric"),c):(t.enter("characterReferenceValue"),r=31,a=St,s(f))}function c(f){return f===88||f===120?(t.enter("characterReferenceMarkerHexadecimal"),t.consume(f),t.exit("characterReferenceMarkerHexadecimal"),t.enter("characterReferenceValue"),r=6,a=g1,s):(t.enter("characterReferenceValue"),r=7,a=la,s(f))}function s(f){if(f===59&&i){let p=t.exit("characterReferenceValue");return a===St&&!Ri(l.sliceSerialize(p))?n(f):(t.enter("characterReferenceMarker"),t.consume(f),t.exit("characterReferenceMarker"),t.exit("characterReference"),e)}return a(f)&&i++<r?(t.consume(f),s):n(f)}}var k1={partial:!0,tokenize:Ik},no={concrete:!0,name:"codeFenced",tokenize:Kk};function Kk(t,e,n){let l=this,i={partial:!0,tokenize:z},r=0,a=0,u;return o;function o(E){return c(E)}function c(E){let M=l.events[l.events.length-1];return r=M&&M[1].type==="linePrefix"?M[2].sliceSerialize(M[1],!0).length:0,u=E,t.enter("codeFenced"),t.enter("codeFencedFence"),t.enter("codeFencedFenceSequence"),s(E)}function s(E){return E===u?(a++,t.consume(E),s):a<3?n(E):(t.exit("codeFencedFenceSequence"),B(E)?U(t,f,"whitespace")(E):f(E))}function f(E){return E===null||R(E)?(t.exit("codeFencedFence"),l.interrupt?e(E):t.check(k1,v,k)(E)):(t.enter("codeFencedFenceInfo"),t.enter("chunkString",{contentType:"string"}),p(E))}function p(E){return E===null||R(E)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),f(E)):B(E)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),U(t,m,"whitespace")(E)):E===96&&E===u?n(E):(t.consume(E),p)}function m(E){return E===null||R(E)?f(E):(t.enter("codeFencedFenceMeta"),t.enter("chunkString",{contentType:"string"}),y(E))}function y(E){return E===null||R(E)?(t.exit("chunkString"),t.exit("codeFencedFenceMeta"),f(E)):E===96&&E===u?n(E):(t.consume(E),y)}function v(E){return t.attempt(i,k,T)(E)}function T(E){return t.enter("lineEnding"),t.consume(E),t.exit("lineEnding"),h}function h(E){return r>0&&B(E)?U(t,d,"linePrefix",r+1)(E):d(E)}function d(E){return E===null||R(E)?t.check(k1,v,k)(E):(t.enter("codeFlowValue"),g(E))}function g(E){return E===null||R(E)?(t.exit("codeFlowValue"),d(E)):(t.consume(E),g)}function k(E){return t.exit("codeFenced"),e(E)}function z(E,M,O){let L=0;return S;function S(j){return E.enter("lineEnding"),E.consume(j),E.exit("lineEnding"),J}function J(j){return E.enter("codeFencedFence"),B(j)?U(E,Z,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(j):Z(j)}function Z(j){return j===u?(E.enter("codeFencedFenceSequence"),N(j)):O(j)}function N(j){return j===u?(L++,E.consume(j),N):L>=a?(E.exit("codeFencedFenceSequence"),B(j)?U(E,q,"whitespace")(j):q(j)):O(j)}function q(j){return j===null||R(j)?(E.exit("codeFencedFence"),M(j)):O(j)}}}function Ik(t,e,n){let l=this;return i;function i(a){return a===null?n(a):(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),r)}function r(a){return l.parser.lazy[l.now().line]?n(a):e(a)}}var ra={name:"codeIndented",tokenize:Wk},Jk={partial:!0,tokenize:Pk};function Wk(t,e,n){let l=this;return i;function i(c){return t.enter("codeIndented"),U(t,r,"linePrefix",5)(c)}function r(c){let s=l.events[l.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?a(c):n(c)}function a(c){return c===null?o(c):R(c)?t.attempt(Jk,a,o)(c):(t.enter("codeFlowValue"),u(c))}function u(c){return c===null||R(c)?(t.exit("codeFlowValue"),a(c)):(t.consume(c),u)}function o(c){return t.exit("codeIndented"),e(c)}}function Pk(t,e,n){let l=this;return i;function i(a){return l.parser.lazy[l.now().line]?n(a):R(a)?(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),i):U(t,r,"linePrefix",5)(a)}function r(a){let u=l.events[l.events.length-1];return u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?e(a):R(a)?i(a):n(a)}}var Df={name:"codeText",previous:tE,resolve:$k,tokenize:eE};function $k(t){let e=t.length-4,n=3,l,i;if((t[n][1].type==="lineEnding"||t[n][1].type==="space")&&(t[e][1].type==="lineEnding"||t[e][1].type==="space")){for(l=n;++l<e;)if(t[l][1].type==="codeTextData"){t[n][1].type="codeTextPadding",t[e][1].type="codeTextPadding",n+=2,e-=2;break}}for(l=n-1,e++;++l<=e;)i===void 0?l!==e&&t[l][1].type!=="lineEnding"&&(i=l):(l===e||t[l][1].type==="lineEnding")&&(t[i][1].type="codeTextData",l!==i+2&&(t[i][1].end=t[l-1][1].end,t.splice(i+2,l-i-2),e-=l-i-2,l=i+2),i=void 0);return t}function tE(t){return t!==96||this.events[this.events.length-1][1].type==="characterEscape"}function eE(t,e,n){let l=this,i=0,r,a;return u;function u(p){return t.enter("codeText"),t.enter("codeTextSequence"),o(p)}function o(p){return p===96?(t.consume(p),i++,o):(t.exit("codeTextSequence"),c(p))}function c(p){return p===null?n(p):p===32?(t.enter("space"),t.consume(p),t.exit("space"),c):p===96?(a=t.enter("codeTextSequence"),r=0,f(p)):R(p)?(t.enter("lineEnding"),t.consume(p),t.exit("lineEnding"),c):(t.enter("codeTextData"),s(p))}function s(p){return p===null||p===32||p===96||R(p)?(t.exit("codeTextData"),c(p)):(t.consume(p),s)}function f(p){return p===96?(t.consume(p),r++,f):r===i?(t.exit("codeTextSequence"),t.exit("codeText"),e(p)):(a.type="codeTextData",s(p))}}var lo=class{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,n){let l=n==null?Number.POSITIVE_INFINITY:n;return l<this.left.length?this.left.slice(e,l):e>this.left.length?this.right.slice(this.right.length-l+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-l+this.left.length).reverse())}splice(e,n,l){let i=n||0;this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return l&&aa(this.left,l),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),aa(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),aa(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&this.right.length===0||e<0&&this.left.length===0))if(e<this.left.length){let n=this.left.splice(e,Number.POSITIVE_INFINITY);aa(this.right,n.reverse())}else{let n=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);aa(this.left,n.reverse())}}};function aa(t,e){let n=0;if(e.length<1e4)t.push(...e);else for(;n<e.length;)t.push(...e.slice(n,n+1e4)),n+=1e4}function io(t){let e={},n=-1,l,i,r,a,u,o,c,s=new lo(t);for(;++n<s.length;){for(;n in e;)n=e[n];if(l=s.get(n),n&&l[1].type==="chunkFlow"&&s.get(n-1)[1].type==="listItemPrefix"&&(o=l[1]._tokenizer.events,r=0,r<o.length&&o[r][1].type==="lineEndingBlank"&&(r+=2),r<o.length&&o[r][1].type==="content"))for(;++r<o.length&&o[r][1].type!=="content";)o[r][1].type==="chunkText"&&(o[r][1]._isInFirstContentOfListItem=!0,r++);if(l[0]==="enter")l[1].contentType&&(Object.assign(e,nE(s,n)),n=e[n],c=!0);else if(l[1]._container){for(r=n,i=void 0;r--;)if(a=s.get(r),a[1].type==="lineEnding"||a[1].type==="lineEndingBlank")a[0]==="enter"&&(i&&(s.get(i)[1].type="lineEndingBlank"),a[1].type="lineEnding",i=r);else if(!(a[1].type==="linePrefix"||a[1].type==="listItemIndent"))break;i&&(l[1].end=C({},s.get(i)[1].start),u=s.slice(i,n),u.unshift(l),s.splice(i,n-i+1,u))}}return At(t,0,Number.POSITIVE_INFINITY,s.slice(0)),!c}function nE(t,e){let n=t.get(e)[1],l=t.get(e)[2],i=e-1,r=[],a=n._tokenizer;a||(a=l.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(a._contentTypeTextTrailing=!0));let u=a.events,o=[],c={},s,f,p=-1,m=n,y=0,v=0,T=[v];for(;m;){for(;t.get(++i)[1]!==m;);r.push(i),m._tokenizer||(s=l.sliceStream(m),m.next||s.push(null),f&&a.defineSkip(m.start),m._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(s),m._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),f=m,m=m.next}for(m=n;++p<u.length;)u[p][0]==="exit"&&u[p-1][0]==="enter"&&u[p][1].type===u[p-1][1].type&&u[p][1].start.line!==u[p][1].end.line&&(v=p+1,T.push(v),m._tokenizer=void 0,m.previous=void 0,m=m.next);for(a.events=[],m?(m._tokenizer=void 0,m.previous=void 0):T.pop(),p=T.length;p--;){let h=u.slice(T[p],T[p+1]),d=r.pop();o.push([d,d+h.length-1]),t.splice(d,2,h)}for(o.reverse(),p=-1;++p<o.length;)c[y+o[p][0]]=y+o[p][1],y+=o[p][1]-o[p][0]-1;return c}var Mf={resolve:iE,tokenize:rE},lE={partial:!0,tokenize:aE};function iE(t){return io(t),t}function rE(t,e){let n;return l;function l(u){return t.enter("content"),n=t.enter("chunkContent",{contentType:"content"}),i(u)}function i(u){return u===null?r(u):R(u)?t.check(lE,a,r)(u):(t.consume(u),i)}function r(u){return t.exit("chunkContent"),t.exit("content"),e(u)}function a(u){return t.consume(u),t.exit("chunkContent"),n.next=t.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function aE(t,e,n){let l=this;return i;function i(a){return t.exit("chunkContent"),t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),U(t,r,"linePrefix")}function r(a){if(a===null||R(a))return n(a);let u=l.events[l.events.length-1];return!l.parser.constructs.disable.null.includes("codeIndented")&&u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?e(a):t.interrupt(l.parser.constructs.flow,n,e)(a)}}function ro(t,e,n,l,i,r,a,u,o){let c=o||Number.POSITIVE_INFINITY,s=0;return f;function f(h){return h===60?(t.enter(l),t.enter(i),t.enter(r),t.consume(h),t.exit(r),p):h===null||h===32||h===41||zl(h)?n(h):(t.enter(l),t.enter(a),t.enter(u),t.enter("chunkString",{contentType:"string"}),v(h))}function p(h){return h===62?(t.enter(r),t.consume(h),t.exit(r),t.exit(i),t.exit(l),e):(t.enter(u),t.enter("chunkString",{contentType:"string"}),m(h))}function m(h){return h===62?(t.exit("chunkString"),t.exit(u),p(h)):h===null||h===60||R(h)?n(h):(t.consume(h),h===92?y:m)}function y(h){return h===60||h===62||h===92?(t.consume(h),m):m(h)}function v(h){return!s&&(h===null||h===41||Q(h))?(t.exit("chunkString"),t.exit(u),t.exit(a),t.exit(l),e(h)):s<c&&h===40?(t.consume(h),s++,v):h===41?(t.consume(h),s--,v):h===null||h===32||h===40||zl(h)?n(h):(t.consume(h),h===92?T:v)}function T(h){return h===40||h===41||h===92?(t.consume(h),v):v(h)}}function ao(t,e,n,l,i,r){let a=this,u=0,o;return c;function c(m){return t.enter(l),t.enter(i),t.consume(m),t.exit(i),t.enter(r),s}function s(m){return u>999||m===null||m===91||m===93&&!o||m===94&&!u&&"_hiddenFootnoteSupport"in a.parser.constructs?n(m):m===93?(t.exit(r),t.enter(i),t.consume(m),t.exit(i),t.exit(l),e):R(m)?(t.enter("lineEnding"),t.consume(m),t.exit("lineEnding"),s):(t.enter("chunkString",{contentType:"string"}),f(m))}function f(m){return m===null||m===91||m===93||R(m)||u++>999?(t.exit("chunkString"),s(m)):(t.consume(m),o||(o=!B(m)),m===92?p:f)}function p(m){return m===91||m===92||m===93?(t.consume(m),u++,f):f(m)}}function uo(t,e,n,l,i,r){let a;return u;function u(p){return p===34||p===39||p===40?(t.enter(l),t.enter(i),t.consume(p),t.exit(i),a=p===40?41:p,o):n(p)}function o(p){return p===a?(t.enter(i),t.consume(p),t.exit(i),t.exit(l),e):(t.enter(r),c(p))}function c(p){return p===a?(t.exit(r),o(a)):p===null?n(p):R(p)?(t.enter("lineEnding"),t.consume(p),t.exit("lineEnding"),U(t,c,"linePrefix")):(t.enter("chunkString",{contentType:"string"}),s(p))}function s(p){return p===a||p===null||R(p)?(t.exit("chunkString"),c(p)):(t.consume(p),p===92?f:s)}function f(p){return p===a||p===92?(t.consume(p),s):s(p)}}function Dl(t,e){let n;return l;function l(i){return R(i)?(t.enter("lineEnding"),t.consume(i),t.exit("lineEnding"),n=!0,l):B(i)?U(t,l,n?"linePrefix":"lineSuffix")(i):e(i)}}var Of={name:"definition",tokenize:oE},uE={partial:!0,tokenize:cE};function oE(t,e,n){let l=this,i;return r;function r(m){return t.enter("definition"),a(m)}function a(m){return ao.call(l,t,u,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(m)}function u(m){return i=Jt(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)),m===58?(t.enter("definitionMarker"),t.consume(m),t.exit("definitionMarker"),o):n(m)}function o(m){return Q(m)?Dl(t,c)(m):c(m)}function c(m){return ro(t,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(m)}function s(m){return t.attempt(uE,f,f)(m)}function f(m){return B(m)?U(t,p,"whitespace")(m):p(m)}function p(m){return m===null||R(m)?(t.exit("definition"),l.parser.defined.push(i),e(m)):n(m)}}function cE(t,e,n){return l;function l(u){return Q(u)?Dl(t,i)(u):n(u)}function i(u){return uo(t,r,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(u)}function r(u){return B(u)?U(t,a,"whitespace")(u):a(u)}function a(u){return u===null||R(u)?e(u):n(u)}}var _f={name:"hardBreakEscape",tokenize:sE};function sE(t,e,n){return l;function l(r){return t.enter("hardBreakEscape"),t.consume(r),i}function i(r){return R(r)?(t.exit("hardBreakEscape"),e(r)):n(r)}}var Rf={name:"headingAtx",resolve:fE,tokenize:mE};function fE(t,e){let n=t.length-2,l=3,i,r;return t[l][1].type==="whitespace"&&(l+=2),n-2>l&&t[n][1].type==="whitespace"&&(n-=2),t[n][1].type==="atxHeadingSequence"&&(l===n-1||n-4>l&&t[n-2][1].type==="whitespace")&&(n-=l+1===n?2:4),n>l&&(i={type:"atxHeadingText",start:t[l][1].start,end:t[n][1].end},r={type:"chunkText",start:t[l][1].start,end:t[n][1].end,contentType:"text"},At(t,l,n-l+1,[["enter",i,e],["enter",r,e],["exit",r,e],["exit",i,e]])),t}function mE(t,e,n){let l=0;return i;function i(s){return t.enter("atxHeading"),r(s)}function r(s){return t.enter("atxHeadingSequence"),a(s)}function a(s){return s===35&&l++<6?(t.consume(s),a):s===null||Q(s)?(t.exit("atxHeadingSequence"),u(s)):n(s)}function u(s){return s===35?(t.enter("atxHeadingSequence"),o(s)):s===null||R(s)?(t.exit("atxHeading"),e(s)):B(s)?U(t,u,"whitespace")(s):(t.enter("atxHeadingText"),c(s))}function o(s){return s===35?(t.consume(s),o):(t.exit("atxHeadingSequence"),u(s))}function c(s){return s===null||s===35||Q(s)?(t.exit("atxHeadingText"),u(s)):(t.consume(s),c)}}var E1=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Nf=["pre","script","style","textarea"];var Lf={concrete:!0,name:"htmlFlow",resolveTo:dE,tokenize:gE},pE={partial:!0,tokenize:xE},hE={partial:!0,tokenize:yE};function dE(t){let e=t.length;for(;e--&&!(t[e][0]==="enter"&&t[e][1].type==="htmlFlow"););return e>1&&t[e-2][1].type==="linePrefix"&&(t[e][1].start=t[e-2][1].start,t[e+1][1].start=t[e-2][1].start,t.splice(e-2,2)),t}function gE(t,e,n){let l=this,i,r,a,u,o;return c;function c(b){return s(b)}function s(b){return t.enter("htmlFlow"),t.enter("htmlFlowData"),t.consume(b),f}function f(b){return b===33?(t.consume(b),p):b===47?(t.consume(b),r=!0,v):b===63?(t.consume(b),i=3,l.interrupt?e:x):Rt(b)?(t.consume(b),a=String.fromCharCode(b),T):n(b)}function p(b){return b===45?(t.consume(b),i=2,m):b===91?(t.consume(b),i=5,u=0,y):Rt(b)?(t.consume(b),i=4,l.interrupt?e:x):n(b)}function m(b){return b===45?(t.consume(b),l.interrupt?e:x):n(b)}function y(b){let Yt="CDATA[";return b===Yt.charCodeAt(u++)?(t.consume(b),u===Yt.length?l.interrupt?e:Z:y):n(b)}function v(b){return Rt(b)?(t.consume(b),a=String.fromCharCode(b),T):n(b)}function T(b){if(b===null||b===47||b===62||Q(b)){let Yt=b===47,ce=a.toLowerCase();return!Yt&&!r&&Nf.includes(ce)?(i=1,l.interrupt?e(b):Z(b)):E1.includes(a.toLowerCase())?(i=6,Yt?(t.consume(b),h):l.interrupt?e(b):Z(b)):(i=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(b):r?d(b):g(b))}return b===45||St(b)?(t.consume(b),a+=String.fromCharCode(b),T):n(b)}function h(b){return b===62?(t.consume(b),l.interrupt?e:Z):n(b)}function d(b){return B(b)?(t.consume(b),d):S(b)}function g(b){return b===47?(t.consume(b),S):b===58||b===95||Rt(b)?(t.consume(b),k):B(b)?(t.consume(b),g):S(b)}function k(b){return b===45||b===46||b===58||b===95||St(b)?(t.consume(b),k):z(b)}function z(b){return b===61?(t.consume(b),E):B(b)?(t.consume(b),z):g(b)}function E(b){return b===null||b===60||b===61||b===62||b===96?n(b):b===34||b===39?(t.consume(b),o=b,M):B(b)?(t.consume(b),E):O(b)}function M(b){return b===o?(t.consume(b),o=null,L):b===null||R(b)?n(b):(t.consume(b),M)}function O(b){return b===null||b===34||b===39||b===47||b===60||b===61||b===62||b===96||Q(b)?z(b):(t.consume(b),O)}function L(b){return b===47||b===62||B(b)?g(b):n(b)}function S(b){return b===62?(t.consume(b),J):n(b)}function J(b){return b===null||R(b)?Z(b):B(b)?(t.consume(b),J):n(b)}function Z(b){return b===45&&i===2?(t.consume(b),it):b===60&&i===1?(t.consume(b),gt):b===62&&i===4?(t.consume(b),Dt):b===63&&i===3?(t.consume(b),x):b===93&&i===5?(t.consume(b),_e):R(b)&&(i===6||i===7)?(t.exit("htmlFlowData"),t.check(pE,wt,N)(b)):b===null||R(b)?(t.exit("htmlFlowData"),N(b)):(t.consume(b),Z)}function N(b){return t.check(hE,q,wt)(b)}function q(b){return t.enter("lineEnding"),t.consume(b),t.exit("lineEnding"),j}function j(b){return b===null||R(b)?N(b):(t.enter("htmlFlowData"),Z(b))}function it(b){return b===45?(t.consume(b),x):Z(b)}function gt(b){return b===47?(t.consume(b),a="",jt):Z(b)}function jt(b){if(b===62){let Yt=a.toLowerCase();return Nf.includes(Yt)?(t.consume(b),Dt):Z(b)}return Rt(b)&&a.length<8?(t.consume(b),a+=String.fromCharCode(b),jt):Z(b)}function _e(b){return b===93?(t.consume(b),x):Z(b)}function x(b){return b===62?(t.consume(b),Dt):b===45&&i===2?(t.consume(b),x):Z(b)}function Dt(b){return b===null||R(b)?(t.exit("htmlFlowData"),wt(b)):(t.consume(b),Dt)}function wt(b){return t.exit("htmlFlow"),e(b)}}function yE(t,e,n){let l=this;return i;function i(a){return R(a)?(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),r):n(a)}function r(a){return l.parser.lazy[l.now().line]?n(a):e(a)}}function xE(t,e,n){return l;function l(i){return t.enter("lineEnding"),t.consume(i),t.exit("lineEnding"),t.attempt(Ke,e,n)}}var Uf={name:"htmlText",tokenize:bE};function bE(t,e,n){let l=this,i,r,a;return u;function u(x){return t.enter("htmlText"),t.enter("htmlTextData"),t.consume(x),o}function o(x){return x===33?(t.consume(x),c):x===47?(t.consume(x),z):x===63?(t.consume(x),g):Rt(x)?(t.consume(x),O):n(x)}function c(x){return x===45?(t.consume(x),s):x===91?(t.consume(x),r=0,y):Rt(x)?(t.consume(x),d):n(x)}function s(x){return x===45?(t.consume(x),m):n(x)}function f(x){return x===null?n(x):x===45?(t.consume(x),p):R(x)?(a=f,gt(x)):(t.consume(x),f)}function p(x){return x===45?(t.consume(x),m):f(x)}function m(x){return x===62?it(x):x===45?p(x):f(x)}function y(x){let Dt="CDATA[";return x===Dt.charCodeAt(r++)?(t.consume(x),r===Dt.length?v:y):n(x)}function v(x){return x===null?n(x):x===93?(t.consume(x),T):R(x)?(a=v,gt(x)):(t.consume(x),v)}function T(x){return x===93?(t.consume(x),h):v(x)}function h(x){return x===62?it(x):x===93?(t.consume(x),h):v(x)}function d(x){return x===null||x===62?it(x):R(x)?(a=d,gt(x)):(t.consume(x),d)}function g(x){return x===null?n(x):x===63?(t.consume(x),k):R(x)?(a=g,gt(x)):(t.consume(x),g)}function k(x){return x===62?it(x):g(x)}function z(x){return Rt(x)?(t.consume(x),E):n(x)}function E(x){return x===45||St(x)?(t.consume(x),E):M(x)}function M(x){return R(x)?(a=M,gt(x)):B(x)?(t.consume(x),M):it(x)}function O(x){return x===45||St(x)?(t.consume(x),O):x===47||x===62||Q(x)?L(x):n(x)}function L(x){return x===47?(t.consume(x),it):x===58||x===95||Rt(x)?(t.consume(x),S):R(x)?(a=L,gt(x)):B(x)?(t.consume(x),L):it(x)}function S(x){return x===45||x===46||x===58||x===95||St(x)?(t.consume(x),S):J(x)}function J(x){return x===61?(t.consume(x),Z):R(x)?(a=J,gt(x)):B(x)?(t.consume(x),J):L(x)}function Z(x){return x===null||x===60||x===61||x===62||x===96?n(x):x===34||x===39?(t.consume(x),i=x,N):R(x)?(a=Z,gt(x)):B(x)?(t.consume(x),Z):(t.consume(x),q)}function N(x){return x===i?(t.consume(x),i=void 0,j):x===null?n(x):R(x)?(a=N,gt(x)):(t.consume(x),N)}function q(x){return x===null||x===34||x===39||x===60||x===61||x===96?n(x):x===47||x===62||Q(x)?L(x):(t.consume(x),q)}function j(x){return x===47||x===62||Q(x)?L(x):n(x)}function it(x){return x===62?(t.consume(x),t.exit("htmlTextData"),t.exit("htmlText"),e):n(x)}function gt(x){return t.exit("htmlTextData"),t.enter("lineEnding"),t.consume(x),t.exit("lineEnding"),jt}function jt(x){return B(x)?U(t,_e,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(x):_e(x)}function _e(x){return t.enter("htmlTextData"),a(x)}}var Ml={name:"labelEnd",resolveAll:EE,resolveTo:TE,tokenize:AE},vE={tokenize:wE},SE={tokenize:zE},kE={tokenize:CE};function EE(t){let e=-1,n=[];for(;++e<t.length;){let l=t[e][1];if(n.push(t[e]),l.type==="labelImage"||l.type==="labelLink"||l.type==="labelEnd"){let i=l.type==="labelImage"?4:2;l.type="data",e+=i}}return t.length!==n.length&&At(t,0,t.length,n),t}function TE(t,e){let n=t.length,l=0,i,r,a,u;for(;n--;)if(i=t[n][1],r){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;t[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(a){if(t[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(r=n,i.type!=="labelLink")){l=2;break}}else i.type==="labelEnd"&&(a=n);let o={type:t[r][1].type==="labelLink"?"link":"image",start:C({},t[r][1].start),end:C({},t[t.length-1][1].end)},c={type:"label",start:C({},t[r][1].start),end:C({},t[a][1].end)},s={type:"labelText",start:C({},t[r+l+2][1].end),end:C({},t[a-2][1].start)};return u=[["enter",o,e],["enter",c,e]],u=ne(u,t.slice(r+1,r+l+3)),u=ne(u,[["enter",s,e]]),u=ne(u,Wn(e.parser.constructs.insideSpan.null,t.slice(r+l+4,a-3),e)),u=ne(u,[["exit",s,e],t[a-2],t[a-1],["exit",c,e]]),u=ne(u,t.slice(a+1)),u=ne(u,[["exit",o,e]]),At(t,r,t.length,u),t}function AE(t,e,n){let l=this,i=l.events.length,r,a;for(;i--;)if((l.events[i][1].type==="labelImage"||l.events[i][1].type==="labelLink")&&!l.events[i][1]._balanced){r=l.events[i][1];break}return u;function u(p){return r?r._inactive?f(p):(a=l.parser.defined.includes(Jt(l.sliceSerialize({start:r.end,end:l.now()}))),t.enter("labelEnd"),t.enter("labelMarker"),t.consume(p),t.exit("labelMarker"),t.exit("labelEnd"),o):n(p)}function o(p){return p===40?t.attempt(vE,s,a?s:f)(p):p===91?t.attempt(SE,s,a?c:f)(p):a?s(p):f(p)}function c(p){return t.attempt(kE,s,f)(p)}function s(p){return e(p)}function f(p){return r._balanced=!0,n(p)}}function wE(t,e,n){return l;function l(f){return t.enter("resource"),t.enter("resourceMarker"),t.consume(f),t.exit("resourceMarker"),i}function i(f){return Q(f)?Dl(t,r)(f):r(f)}function r(f){return f===41?s(f):ro(t,a,u,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(f)}function a(f){return Q(f)?Dl(t,o)(f):s(f)}function u(f){return n(f)}function o(f){return f===34||f===39||f===40?uo(t,c,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(f):s(f)}function c(f){return Q(f)?Dl(t,s)(f):s(f)}function s(f){return f===41?(t.enter("resourceMarker"),t.consume(f),t.exit("resourceMarker"),t.exit("resource"),e):n(f)}}function zE(t,e,n){let l=this;return i;function i(u){return ao.call(l,t,r,a,"reference","referenceMarker","referenceString")(u)}function r(u){return l.parser.defined.includes(Jt(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)))?e(u):n(u)}function a(u){return n(u)}}function CE(t,e,n){return l;function l(r){return t.enter("reference"),t.enter("referenceMarker"),t.consume(r),t.exit("referenceMarker"),i}function i(r){return r===93?(t.enter("referenceMarker"),t.consume(r),t.exit("referenceMarker"),t.exit("reference"),e):n(r)}}var Bf={name:"labelStartImage",resolveAll:Ml.resolveAll,tokenize:DE};function DE(t,e,n){let l=this;return i;function i(u){return t.enter("labelImage"),t.enter("labelImageMarker"),t.consume(u),t.exit("labelImageMarker"),r}function r(u){return u===91?(t.enter("labelMarker"),t.consume(u),t.exit("labelMarker"),t.exit("labelImage"),a):n(u)}function a(u){return u===94&&"_hiddenFootnoteSupport"in l.parser.constructs?n(u):e(u)}}var Hf={name:"labelStartLink",resolveAll:Ml.resolveAll,tokenize:ME};function ME(t,e,n){let l=this;return i;function i(a){return t.enter("labelLink"),t.enter("labelMarker"),t.consume(a),t.exit("labelMarker"),t.exit("labelLink"),r}function r(a){return a===94&&"_hiddenFootnoteSupport"in l.parser.constructs?n(a):e(a)}}var ua={name:"lineEnding",tokenize:OE};function OE(t,e){return n;function n(l){return t.enter("lineEnding"),t.consume(l),t.exit("lineEnding"),U(t,e,"linePrefix")}}var Ol={name:"thematicBreak",tokenize:_E};function _E(t,e,n){let l=0,i;return r;function r(c){return t.enter("thematicBreak"),a(c)}function a(c){return i=c,u(c)}function u(c){return c===i?(t.enter("thematicBreakSequence"),o(c)):l>=3&&(c===null||R(c))?(t.exit("thematicBreak"),e(c)):n(c)}function o(c){return c===i?(t.consume(c),l++,o):(t.exit("thematicBreakSequence"),B(c)?U(t,u,"whitespace")(c):u(c))}}var Wt={continuation:{tokenize:UE},exit:HE,name:"list",tokenize:LE},RE={partial:!0,tokenize:qE},NE={partial:!0,tokenize:BE};function LE(t,e,n){let l=this,i=l.events[l.events.length-1],r=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,a=0;return u;function u(m){let y=l.containerState.type||(m===42||m===43||m===45?"listUnordered":"listOrdered");if(y==="listUnordered"?!l.containerState.marker||m===l.containerState.marker:la(m)){if(l.containerState.type||(l.containerState.type=y,t.enter(y,{_container:!0})),y==="listUnordered")return t.enter("listItemPrefix"),m===42||m===45?t.check(Ol,n,c)(m):c(m);if(!l.interrupt||m===49)return t.enter("listItemPrefix"),t.enter("listItemValue"),o(m)}return n(m)}function o(m){return la(m)&&++a<10?(t.consume(m),o):(!l.interrupt||a<2)&&(l.containerState.marker?m===l.containerState.marker:m===41||m===46)?(t.exit("listItemValue"),c(m)):n(m)}function c(m){return t.enter("listItemMarker"),t.consume(m),t.exit("listItemMarker"),l.containerState.marker=l.containerState.marker||m,t.check(Ke,l.interrupt?n:s,t.attempt(RE,p,f))}function s(m){return l.containerState.initialBlankLine=!0,r++,p(m)}function f(m){return B(m)?(t.enter("listItemPrefixWhitespace"),t.consume(m),t.exit("listItemPrefixWhitespace"),p):n(m)}function p(m){return l.containerState.size=r+l.sliceSerialize(t.exit("listItemPrefix"),!0).length,e(m)}}function UE(t,e,n){let l=this;return l.containerState._closeFlow=void 0,t.check(Ke,i,r);function i(u){return l.containerState.furtherBlankLines=l.containerState.furtherBlankLines||l.containerState.initialBlankLine,U(t,e,"listItemIndent",l.containerState.size+1)(u)}function r(u){return l.containerState.furtherBlankLines||!B(u)?(l.containerState.furtherBlankLines=void 0,l.containerState.initialBlankLine=void 0,a(u)):(l.containerState.furtherBlankLines=void 0,l.containerState.initialBlankLine=void 0,t.attempt(NE,e,a)(u))}function a(u){return l.containerState._closeFlow=!0,l.interrupt=void 0,U(t,t.attempt(Wt,e,n),"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(u)}}function BE(t,e,n){let l=this;return U(t,i,"listItemIndent",l.containerState.size+1);function i(r){let a=l.events[l.events.length-1];return a&&a[1].type==="listItemIndent"&&a[2].sliceSerialize(a[1],!0).length===l.containerState.size?e(r):n(r)}}function HE(t){t.exit(this.containerState.type)}function qE(t,e,n){let l=this;return U(t,i,"listItemPrefixWhitespace",l.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(r){let a=l.events[l.events.length-1];return!B(r)&&a&&a[1].type==="listItemPrefixWhitespace"?e(r):n(r)}}var oo={name:"setextUnderline",resolveTo:jE,tokenize:YE};function jE(t,e){let n=t.length,l,i,r;for(;n--;)if(t[n][0]==="enter"){if(t[n][1].type==="content"){l=n;break}t[n][1].type==="paragraph"&&(i=n)}else t[n][1].type==="content"&&t.splice(n,1),!r&&t[n][1].type==="definition"&&(r=n);let a={type:"setextHeading",start:C({},t[l][1].start),end:C({},t[t.length-1][1].end)};return t[i][1].type="setextHeadingText",r?(t.splice(i,0,["enter",a,e]),t.splice(r+1,0,["exit",t[l][1],e]),t[l][1].end=C({},t[r][1].end)):t[l][1]=a,t.push(["exit",a,e]),t}function YE(t,e,n){let l=this,i;return r;function r(c){let s=l.events.length,f;for(;s--;)if(l.events[s][1].type!=="lineEnding"&&l.events[s][1].type!=="linePrefix"&&l.events[s][1].type!=="content"){f=l.events[s][1].type==="paragraph";break}return!l.parser.lazy[l.now().line]&&(l.interrupt||f)?(t.enter("setextHeadingLine"),i=c,a(c)):n(c)}function a(c){return t.enter("setextHeadingLineSequence"),u(c)}function u(c){return c===i?(t.consume(c),u):(t.exit("setextHeadingLineSequence"),B(c)?U(t,o,"lineSuffix")(c):o(c))}function o(c){return c===null||R(c)?(t.exit("setextHeadingLine"),e(c)):n(c)}}var T1={tokenize:VE};function VE(t){let e=this,n=t.attempt(Ke,l,t.attempt(this.parser.constructs.flowInitial,i,U(t,t.attempt(this.parser.constructs.flow,i,t.attempt(Mf,i)),"linePrefix")));return n;function l(r){if(r===null){t.consume(r);return}return t.enter("lineEndingBlank"),t.consume(r),t.exit("lineEndingBlank"),e.currentConstruct=void 0,n}function i(r){if(r===null){t.consume(r);return}return t.enter("lineEnding"),t.consume(r),t.exit("lineEnding"),e.currentConstruct=void 0,n}}var A1={resolveAll:D1()},w1=C1("string"),z1=C1("text");function C1(t){return{resolveAll:D1(t==="text"?XE:void 0),tokenize:e};function e(n){let l=this,i=this.parser.constructs[t],r=n.attempt(i,a,u);return a;function a(s){return c(s)?r(s):u(s)}function u(s){if(s===null){n.consume(s);return}return n.enter("data"),n.consume(s),o}function o(s){return c(s)?(n.exit("data"),r(s)):(n.consume(s),o)}function c(s){if(s===null)return!0;let f=i[s],p=-1;if(f)for(;++p<f.length;){let m=f[p];if(!m.previous||m.previous.call(l,l.previous))return!0}return!1}}}function D1(t){return e;function e(n,l){let i=-1,r;for(;++i<=n.length;)r===void 0?n[i]&&n[i][1].type==="data"&&(r=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==r+2&&(n[r][1].end=n[i-1][1].end,n.splice(r+2,i-r-2),i=r+2),r=void 0);return t?t(n,l):n}}function XE(t,e){let n=0;for(;++n<=t.length;)if((n===t.length||t[n][1].type==="lineEnding")&&t[n-1][1].type==="data"){let l=t[n-1][1],i=e.sliceStream(l),r=i.length,a=-1,u=0,o;for(;r--;){let c=i[r];if(typeof c=="string"){for(a=c.length;c.charCodeAt(a-1)===32;)u++,a--;if(a)break;a=-1}else if(c===-2)o=!0,u++;else if(c!==-1){r++;break}}if(e._contentTypeTextTrailing&&n===t.length&&(u=0),u){let c={type:n===t.length||o||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:r?a:l.start._bufferIndex+a,_index:l.start._index+r,line:l.end.line,column:l.end.column-u,offset:l.end.offset-u},end:C({},l.end)};l.end=C({},c.start),l.start.offset===l.end.offset?Object.assign(l,c):(t.splice(n,0,["enter",c,e],["exit",c,e]),n+=2)}n++}return t}var qf={};$m(qf,{attentionMarkers:()=>WE,contentInitial:()=>QE,disable:()=>PE,document:()=>GE,flow:()=>FE,flowInitial:()=>ZE,insideSpan:()=>JE,string:()=>KE,text:()=>IE});var GE={42:Wt,43:Wt,45:Wt,48:Wt,49:Wt,50:Wt,51:Wt,52:Wt,53:Wt,54:Wt,55:Wt,56:Wt,57:Wt,62:$u},QE={91:Of},ZE={[-2]:ra,[-1]:ra,32:ra},FE={35:Rf,42:Ol,45:[oo,Ol],60:Lf,61:oo,95:Ol,96:no,126:no},KE={38:eo,92:to},IE={[-5]:ua,[-4]:ua,[-3]:ua,33:Bf,38:eo,42:ia,60:[Cf,Uf],91:Hf,92:[_f,to],93:Ml,95:ia,96:Df},JE={null:[ia,A1]},WE={null:[42,95]},PE={null:[]};function M1(t,e,n){let l={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},r=[],a=[],u=[],o=!0,c={attempt:L(M),check:L(O),consume:k,enter:z,exit:E,interrupt:L(O,{interrupt:!0})},s={code:null,containerState:{},defineSkip:h,events:[],now:T,parser:t,previous:null,sliceSerialize:y,sliceStream:v,write:m},f=e.tokenize.call(s,c),p;return e.resolveAll&&r.push(e),s;function m(N){return a=ne(a,N),d(),a[a.length-1]!==null?[]:(S(e,0),s.events=Wn(r,s.events,s),s.events)}function y(N,q){return t2(v(N),q)}function v(N){return $E(a,N)}function T(){let{_bufferIndex:N,_index:q,line:j,column:it,offset:gt}=l;return{_bufferIndex:N,_index:q,line:j,column:it,offset:gt}}function h(N){i[N.line]=N.column,Z()}function d(){let N;for(;l._index<a.length;){let q=a[l._index];if(typeof q=="string")for(N=l._index,l._bufferIndex<0&&(l._bufferIndex=0);l._index===N&&l._bufferIndex<q.length;)g(q.charCodeAt(l._bufferIndex));else g(q)}}function g(N){o=void 0,p=N,f=f(N)}function k(N){R(N)?(l.line++,l.column=1,l.offset+=N===-3?2:1,Z()):N!==-1&&(l.column++,l.offset++),l._bufferIndex<0?l._index++:(l._bufferIndex++,l._bufferIndex===a[l._index].length&&(l._bufferIndex=-1,l._index++)),s.previous=N,o=!0}function z(N,q){let j=q||{};return j.type=N,j.start=T(),s.events.push(["enter",j,s]),u.push(j),j}function E(N){let q=u.pop();return q.end=T(),s.events.push(["exit",q,s]),q}function M(N,q){S(N,q.from)}function O(N,q){q.restore()}function L(N,q){return j;function j(it,gt,jt){let _e,x,Dt,wt;return Array.isArray(it)?Yt(it):"tokenize"in it?Yt([it]):b(it);function b(Vt){return Vi;function Vi(Je){let nl=Je!==null&&Vt[Je],Ul=Je!==null&&Vt.null,zo=[...Array.isArray(nl)?nl:nl?[nl]:[],...Array.isArray(Ul)?Ul:Ul?[Ul]:[]];return Yt(zo)(Je)}}function Yt(Vt){return _e=Vt,x=0,Vt.length===0?jt:ce(Vt[x])}function ce(Vt){return Vi;function Vi(Je){return wt=J(),Dt=Vt,Vt.partial||(s.currentConstruct=Vt),Vt.name&&s.parser.constructs.disable.null.includes(Vt.name)?pa(Je):Vt.tokenize.call(q?Object.assign(Object.create(s),q):s,c,el,pa)(Je)}}function el(Vt){return o=!0,N(Dt,wt),gt}function pa(Vt){return o=!0,wt.restore(),++x<_e.length?ce(_e[x]):jt}}}function S(N,q){N.resolveAll&&!r.includes(N)&&r.push(N),N.resolve&&At(s.events,q,s.events.length-q,N.resolve(s.events.slice(q),s)),N.resolveTo&&(s.events=N.resolveTo(s.events,s))}function J(){let N=T(),q=s.previous,j=s.currentConstruct,it=s.events.length,gt=Array.from(u);return{from:it,restore:jt};function jt(){l=N,s.previous=q,s.currentConstruct=j,s.events.length=it,u=gt,Z()}}function Z(){l.line in i&&l.column<2&&(l.column=i[l.line],l.offset+=i[l.line]-1)}}function $E(t,e){let n=e.start._index,l=e.start._bufferIndex,i=e.end._index,r=e.end._bufferIndex,a;if(n===i)a=[t[n].slice(l,r)];else{if(a=t.slice(n,i),l>-1){let u=a[0];typeof u=="string"?a[0]=u.slice(l):a.shift()}r>0&&a.push(t[i].slice(0,r))}return a}function t2(t,e){let n=-1,l=[],i;for(;++n<t.length;){let r=t[n],a;if(typeof r=="string")a=r;else switch(r){case-5:{a="\r";break}case-4:{a=`
`;break}case-3:{a=`\r
`;break}case-2:{a=e?" ":"	";break}case-1:{if(!e&&i)continue;a=" ";break}default:a=String.fromCharCode(r)}i=r===-2,l.push(a)}return l.join("")}function jf(t){let l={constructs:Wu([qf,...(t||{}).extensions||[]]),content:i(x1),defined:[],document:i(v1),flow:i(T1),lazy:{},string:i(w1),text:i(z1)};return l;function i(r){return a;function a(u){return M1(l,r,u)}}}function Yf(t){for(;!io(t););return t}var O1=/[\0\t\n\r]/g;function Vf(){let t=1,e="",n=!0,l;return i;function i(r,a,u){let o=[],c,s,f,p,m;for(r=e+(typeof r=="string"?r.toString():new TextDecoder(a||void 0).decode(r)),f=0,e="",n&&(r.charCodeAt(0)===65279&&f++,n=void 0);f<r.length;){if(O1.lastIndex=f,c=O1.exec(r),p=c&&c.index!==void 0?c.index:r.length,m=r.charCodeAt(p),!c){e=r.slice(f);break}if(m===10&&f===p&&l)o.push(-3),l=void 0;else switch(l&&(o.push(-5),l=void 0),f<p&&(o.push(r.slice(f,p)),t+=p-f),m){case 0:{o.push(65533),t++;break}case 9:{for(s=Math.ceil(t/4)*4,o.push(-2);t++<s;)o.push(-1);break}case 10:{o.push(-4),t=1;break}default:l=!0,t=1}f=p+1}return u&&(l&&o.push(-5),e&&o.push(e),o.push(null)),o}}var e2=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function _1(t){return t.replace(e2,n2)}function n2(t,e,n){if(e)return e;if(n.charCodeAt(0)===35){let i=n.charCodeAt(1),r=i===120||i===88;return Pu(n.slice(r?2:1),r?16:10)}return Ri(n)||t}var N1={}.hasOwnProperty;function Xf(t,e,n){return typeof e!="string"&&(n=e,e=void 0),l2(n)(Yf(jf(n).document().write(Vf()(t,e,!0))))}function l2(t){let e={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(Fm),autolinkProtocol:L,autolinkEmail:L,atxHeading:r(Gm),blockQuote:r(Je),characterEscape:L,characterReference:L,codeFenced:r(nl),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:r(nl,a),codeText:r(Ul,a),codeTextData:L,data:L,codeFlowValue:L,definition:r(zo),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:r(Cx),hardBreakEscape:r(Qm),hardBreakTrailing:r(Qm),htmlFlow:r(Zm,a),htmlFlowData:L,htmlText:r(Zm,a),htmlTextData:L,image:r(Dx),label:a,link:r(Fm),listItem:r(Mx),listItemValue:p,listOrdered:r(Km,f),listUnordered:r(Km),paragraph:r(Ox),reference:b,referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:r(Gm),strong:r(_x),thematicBreak:r(Nx)},exit:{atxHeading:o(),atxHeadingSequence:z,autolink:o(),autolinkEmail:Vi,autolinkProtocol:Vt,blockQuote:o(),characterEscapeValue:S,characterReferenceMarkerHexadecimal:ce,characterReferenceMarkerNumeric:ce,characterReferenceValue:el,characterReference:pa,codeFenced:o(T),codeFencedFence:v,codeFencedFenceInfo:m,codeFencedFenceMeta:y,codeFlowValue:S,codeIndented:o(h),codeText:o(j),codeTextData:S,data:S,definition:o(),definitionDestinationString:k,definitionLabelString:d,definitionTitleString:g,emphasis:o(),hardBreakEscape:o(Z),hardBreakTrailing:o(Z),htmlFlow:o(N),htmlFlowData:S,htmlText:o(q),htmlTextData:S,image:o(gt),label:_e,labelText:jt,lineEnding:J,link:o(it),listItem:o(),listOrdered:o(),listUnordered:o(),paragraph:o(),referenceString:Yt,resourceDestinationString:x,resourceTitleString:Dt,resource:wt,setextHeading:o(O),setextHeadingLineSequence:M,setextHeadingText:E,strong:o(),thematicBreak:o()}};L1(e,(t||{}).mdastExtensions||[]);let n={};return l;function l(A){let _={type:"root",children:[]},V={stack:[_],tokenStack:[],config:e,enter:u,exit:c,buffer:a,resume:s,data:n},I=[],ut=-1;for(;++ut<A.length;)if(A[ut][1].type==="listOrdered"||A[ut][1].type==="listUnordered")if(A[ut][0]==="enter")I.push(ut);else{let Re=I.pop();ut=i(A,Re,ut)}for(ut=-1;++ut<A.length;){let Re=e[A[ut][0]];N1.call(Re,A[ut][1].type)&&Re[A[ut][1].type].call(Object.assign({sliceSerialize:A[ut][2].sliceSerialize},V),A[ut][1])}if(V.tokenStack.length>0){let Re=V.tokenStack[V.tokenStack.length-1];(Re[1]||R1).call(V,void 0,Re[0])}for(_.position={start:Pn(A.length>0?A[0][1].start:{line:1,column:1,offset:0}),end:Pn(A.length>0?A[A.length-2][1].end:{line:1,column:1,offset:0})},ut=-1;++ut<e.transforms.length;)_=e.transforms[ut](_)||_;return _}function i(A,_,V){let I=_-1,ut=-1,Re=!1,ll,We,Xi,Gi;for(;++I<=V;){let se=A[I];switch(se[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{se[0]==="enter"?ut++:ut--,Gi=void 0;break}case"lineEndingBlank":{se[0]==="enter"&&(ll&&!Gi&&!ut&&!Xi&&(Xi=I),Gi=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:Gi=void 0}if(!ut&&se[0]==="enter"&&se[1].type==="listItemPrefix"||ut===-1&&se[0]==="exit"&&(se[1].type==="listUnordered"||se[1].type==="listOrdered")){if(ll){let Bl=I;for(We=void 0;Bl--;){let Pe=A[Bl];if(Pe[1].type==="lineEnding"||Pe[1].type==="lineEndingBlank"){if(Pe[0]==="exit")continue;We&&(A[We][1].type="lineEndingBlank",Re=!0),Pe[1].type="lineEnding",We=Bl}else if(!(Pe[1].type==="linePrefix"||Pe[1].type==="blockQuotePrefix"||Pe[1].type==="blockQuotePrefixWhitespace"||Pe[1].type==="blockQuoteMarker"||Pe[1].type==="listItemIndent"))break}Xi&&(!We||Xi<We)&&(ll._spread=!0),ll.end=Object.assign({},We?A[We][1].start:se[1].end),A.splice(We||I,0,["exit",ll,se[2]]),I++,V++}if(se[1].type==="listItemPrefix"){let Bl={type:"listItem",_spread:!1,start:Object.assign({},se[1].start),end:void 0};ll=Bl,A.splice(I,0,["enter",Bl,se[2]]),I++,V++,Xi=void 0,Gi=!0}}}return A[_][1]._spread=Re,V}function r(A,_){return V;function V(I){u.call(this,A(I),I),_&&_.call(this,I)}}function a(){this.stack.push({type:"fragment",children:[]})}function u(A,_,V){this.stack[this.stack.length-1].children.push(A),this.stack.push(A),this.tokenStack.push([_,V||void 0]),A.position={start:Pn(_.start),end:void 0}}function o(A){return _;function _(V){A&&A.call(this,V),c.call(this,V)}}function c(A,_){let V=this.stack.pop(),I=this.tokenStack.pop();if(I)I[0].type!==A.type&&(_?_.call(this,A,I[0]):(I[1]||R1).call(this,A,I[0]));else throw new Error("Cannot close `"+A.type+"` ("+In({start:A.start,end:A.end})+"): it\u2019s not open");V.position.end=Pn(A.end)}function s(){return wl(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function p(A){if(this.data.expectingFirstListItemValue){let _=this.stack[this.stack.length-2];_.start=Number.parseInt(this.sliceSerialize(A),10),this.data.expectingFirstListItemValue=void 0}}function m(){let A=this.resume(),_=this.stack[this.stack.length-1];_.lang=A}function y(){let A=this.resume(),_=this.stack[this.stack.length-1];_.meta=A}function v(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function T(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function h(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A.replace(/(\r?\n|\r)$/g,"")}function d(A){let _=this.resume(),V=this.stack[this.stack.length-1];V.label=_,V.identifier=Jt(this.sliceSerialize(A)).toLowerCase()}function g(){let A=this.resume(),_=this.stack[this.stack.length-1];_.title=A}function k(){let A=this.resume(),_=this.stack[this.stack.length-1];_.url=A}function z(A){let _=this.stack[this.stack.length-1];if(!_.depth){let V=this.sliceSerialize(A).length;_.depth=V}}function E(){this.data.setextHeadingSlurpLineEnding=!0}function M(A){let _=this.stack[this.stack.length-1];_.depth=this.sliceSerialize(A).codePointAt(0)===61?1:2}function O(){this.data.setextHeadingSlurpLineEnding=void 0}function L(A){let V=this.stack[this.stack.length-1].children,I=V[V.length-1];(!I||I.type!=="text")&&(I=Rx(),I.position={start:Pn(A.start),end:void 0},V.push(I)),this.stack.push(I)}function S(A){let _=this.stack.pop();_.value+=this.sliceSerialize(A),_.position.end=Pn(A.end)}function J(A){let _=this.stack[this.stack.length-1];if(this.data.atHardBreak){let V=_.children[_.children.length-1];V.position.end=Pn(A.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&e.canContainEols.includes(_.type)&&(L.call(this,A),S.call(this,A))}function Z(){this.data.atHardBreak=!0}function N(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A}function q(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A}function j(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A}function it(){let A=this.stack[this.stack.length-1];if(this.data.inReference){let _=this.data.referenceType||"shortcut";A.type+="Reference",A.referenceType=_,delete A.url,delete A.title}else delete A.identifier,delete A.label;this.data.referenceType=void 0}function gt(){let A=this.stack[this.stack.length-1];if(this.data.inReference){let _=this.data.referenceType||"shortcut";A.type+="Reference",A.referenceType=_,delete A.url,delete A.title}else delete A.identifier,delete A.label;this.data.referenceType=void 0}function jt(A){let _=this.sliceSerialize(A),V=this.stack[this.stack.length-2];V.label=_1(_),V.identifier=Jt(_).toLowerCase()}function _e(){let A=this.stack[this.stack.length-1],_=this.resume(),V=this.stack[this.stack.length-1];if(this.data.inReference=!0,V.type==="link"){let I=A.children;V.children=I}else V.alt=_}function x(){let A=this.resume(),_=this.stack[this.stack.length-1];_.url=A}function Dt(){let A=this.resume(),_=this.stack[this.stack.length-1];_.title=A}function wt(){this.data.inReference=void 0}function b(){this.data.referenceType="collapsed"}function Yt(A){let _=this.resume(),V=this.stack[this.stack.length-1];V.label=_,V.identifier=Jt(this.sliceSerialize(A)).toLowerCase(),this.data.referenceType="full"}function ce(A){this.data.characterReferenceType=A.type}function el(A){let _=this.sliceSerialize(A),V=this.data.characterReferenceType,I;V?(I=Pu(_,V==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):I=Ri(_);let ut=this.stack[this.stack.length-1];ut.value+=I}function pa(A){let _=this.stack.pop();_.position.end=Pn(A.end)}function Vt(A){S.call(this,A);let _=this.stack[this.stack.length-1];_.url=this.sliceSerialize(A)}function Vi(A){S.call(this,A);let _=this.stack[this.stack.length-1];_.url="mailto:"+this.sliceSerialize(A)}function Je(){return{type:"blockquote",children:[]}}function nl(){return{type:"code",lang:null,meta:null,value:""}}function Ul(){return{type:"inlineCode",value:""}}function zo(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Cx(){return{type:"emphasis",children:[]}}function Gm(){return{type:"heading",depth:0,children:[]}}function Qm(){return{type:"break"}}function Zm(){return{type:"html",value:""}}function Dx(){return{type:"image",title:null,url:"",alt:null}}function Fm(){return{type:"link",title:null,url:"",children:[]}}function Km(A){return{type:"list",ordered:A.type==="listOrdered",start:null,spread:A._spread,children:[]}}function Mx(A){return{type:"listItem",spread:A._spread,checked:null,children:[]}}function Ox(){return{type:"paragraph",children:[]}}function _x(){return{type:"strong",children:[]}}function Rx(){return{type:"text",value:""}}function Nx(){return{type:"thematicBreak"}}}function Pn(t){return{line:t.line,column:t.column,offset:t.offset}}function L1(t,e){let n=-1;for(;++n<e.length;){let l=e[n];Array.isArray(l)?L1(t,l):i2(t,l)}}function i2(t,e){let n;for(n in e)if(N1.call(e,n))switch(n){case"canContainEols":{let l=e[n];l&&t[n].push(...l);break}case"transforms":{let l=e[n];l&&t[n].push(...l);break}case"enter":case"exit":{let l=e[n];l&&Object.assign(t[n],l);break}}}function R1(t,e){throw t?new Error("Cannot close `"+t.type+"` ("+In({start:t.start,end:t.end})+"): a different token (`"+e.type+"`, "+In({start:e.start,end:e.end})+") is open"):new Error("Cannot close document, a token (`"+e.type+"`, "+In({start:e.start,end:e.end})+") is still open")}function co(t){let e=this;e.parser=n;function n(l){return Xf(l,Nt(C(C({},e.data("settings")),t),{extensions:e.data("micromarkExtensions")||[],mdastExtensions:e.data("fromMarkdownExtensions")||[]}))}}function U1(t,e){let n={type:"element",tagName:"blockquote",properties:{},children:t.wrap(t.all(e),!0)};return t.patch(e,n),t.applyData(e,n)}function B1(t,e){let n={type:"element",tagName:"br",properties:{},children:[]};return t.patch(e,n),[t.applyData(e,n),{type:"text",value:`
`}]}function H1(t,e){let n=e.value?e.value+`
`:"",l={};e.lang&&(l.className=["language-"+e.lang]);let i={type:"element",tagName:"code",properties:l,children:[{type:"text",value:n}]};return e.meta&&(i.data={meta:e.meta}),t.patch(e,i),i=t.applyData(e,i),i={type:"element",tagName:"pre",properties:{},children:[i]},t.patch(e,i),i}function q1(t,e){let n={type:"element",tagName:"del",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function j1(t,e){let n={type:"element",tagName:"em",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function Y1(t,e){let n=typeof t.options.clobberPrefix=="string"?t.options.clobberPrefix:"user-content-",l=String(e.identifier).toUpperCase(),i=Oe(l.toLowerCase()),r=t.footnoteOrder.indexOf(l),a,u=t.footnoteCounts.get(l);u===void 0?(u=0,t.footnoteOrder.push(l),a=t.footnoteOrder.length):a=r+1,u+=1,t.footnoteCounts.set(l,u);let o={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+i,id:n+"fnref-"+i+(u>1?"-"+u:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(a)}]};t.patch(e,o);let c={type:"element",tagName:"sup",properties:{},children:[o]};return t.patch(e,c),t.applyData(e,c)}function V1(t,e){let n={type:"element",tagName:"h"+e.depth,properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function X1(t,e){if(t.options.allowDangerousHtml){let n={type:"raw",value:e.value};return t.patch(e,n),t.applyData(e,n)}}function so(t,e){let n=e.referenceType,l="]";if(n==="collapsed"?l+="[]":n==="full"&&(l+="["+(e.label||e.identifier)+"]"),e.type==="imageReference")return[{type:"text",value:"!["+e.alt+l}];let i=t.all(e),r=i[0];r&&r.type==="text"?r.value="["+r.value:i.unshift({type:"text",value:"["});let a=i[i.length-1];return a&&a.type==="text"?a.value+=l:i.push({type:"text",value:l}),i}function G1(t,e){let n=String(e.identifier).toUpperCase(),l=t.definitionById.get(n);if(!l)return so(t,e);let i={src:Oe(l.url||""),alt:e.alt};l.title!==null&&l.title!==void 0&&(i.title=l.title);let r={type:"element",tagName:"img",properties:i,children:[]};return t.patch(e,r),t.applyData(e,r)}function Q1(t,e){let n={src:Oe(e.url)};e.alt!==null&&e.alt!==void 0&&(n.alt=e.alt),e.title!==null&&e.title!==void 0&&(n.title=e.title);let l={type:"element",tagName:"img",properties:n,children:[]};return t.patch(e,l),t.applyData(e,l)}function Z1(t,e){let n={type:"text",value:e.value.replace(/\r?\n|\r/g," ")};t.patch(e,n);let l={type:"element",tagName:"code",properties:{},children:[n]};return t.patch(e,l),t.applyData(e,l)}function F1(t,e){let n=String(e.identifier).toUpperCase(),l=t.definitionById.get(n);if(!l)return so(t,e);let i={href:Oe(l.url||"")};l.title!==null&&l.title!==void 0&&(i.title=l.title);let r={type:"element",tagName:"a",properties:i,children:t.all(e)};return t.patch(e,r),t.applyData(e,r)}function K1(t,e){let n={href:Oe(e.url)};e.title!==null&&e.title!==void 0&&(n.title=e.title);let l={type:"element",tagName:"a",properties:n,children:t.all(e)};return t.patch(e,l),t.applyData(e,l)}function I1(t,e,n){let l=t.all(e),i=n?r2(n):J1(e),r={},a=[];if(typeof e.checked=="boolean"){let s=l[0],f;s&&s.type==="element"&&s.tagName==="p"?f=s:(f={type:"element",tagName:"p",properties:{},children:[]},l.unshift(f)),f.children.length>0&&f.children.unshift({type:"text",value:" "}),f.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:e.checked,disabled:!0},children:[]}),r.className=["task-list-item"]}let u=-1;for(;++u<l.length;){let s=l[u];(i||u!==0||s.type!=="element"||s.tagName!=="p")&&a.push({type:"text",value:`
`}),s.type==="element"&&s.tagName==="p"&&!i?a.push(...s.children):a.push(s)}let o=l[l.length-1];o&&(i||o.type!=="element"||o.tagName!=="p")&&a.push({type:"text",value:`
`});let c={type:"element",tagName:"li",properties:r,children:a};return t.patch(e,c),t.applyData(e,c)}function r2(t){let e=!1;if(t.type==="list"){e=t.spread||!1;let n=t.children,l=-1;for(;!e&&++l<n.length;)e=J1(n[l])}return e}function J1(t){let e=t.spread;return e==null?t.children.length>1:e}function W1(t,e){let n={},l=t.all(e),i=-1;for(typeof e.start=="number"&&e.start!==1&&(n.start=e.start);++i<l.length;){let a=l[i];if(a.type==="element"&&a.tagName==="li"&&a.properties&&Array.isArray(a.properties.className)&&a.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let r={type:"element",tagName:e.ordered?"ol":"ul",properties:n,children:t.wrap(l,!0)};return t.patch(e,r),t.applyData(e,r)}function P1(t,e){let n={type:"element",tagName:"p",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function $1(t,e){let n={type:"root",children:t.wrap(t.all(e))};return t.patch(e,n),t.applyData(e,n)}function t0(t,e){let n={type:"element",tagName:"strong",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function e0(t,e){let n=t.all(e),l=n.shift(),i=[];if(l){let a={type:"element",tagName:"thead",properties:{},children:t.wrap([l],!0)};t.patch(e.children[0],a),i.push(a)}if(n.length>0){let a={type:"element",tagName:"tbody",properties:{},children:t.wrap(n,!0)},u=_i(e.children[1]),o=Ku(e.children[e.children.length-1]);u&&o&&(a.position={start:u,end:o}),i.push(a)}let r={type:"element",tagName:"table",properties:{},children:t.wrap(i,!0)};return t.patch(e,r),t.applyData(e,r)}function n0(t,e,n){let l=n?n.children:void 0,r=(l?l.indexOf(e):1)===0?"th":"td",a=n&&n.type==="table"?n.align:void 0,u=a?a.length:e.children.length,o=-1,c=[];for(;++o<u;){let f=e.children[o],p={},m=a?a[o]:void 0;m&&(p.align=m);let y={type:"element",tagName:r,properties:p,children:[]};f&&(y.children=t.all(f),t.patch(f,y),y=t.applyData(f,y)),c.push(y)}let s={type:"element",tagName:"tr",properties:{},children:t.wrap(c,!0)};return t.patch(e,s),t.applyData(e,s)}function l0(t,e){let n={type:"element",tagName:"td",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function r0(t){let e=String(t),n=/\r?\n|\r/g,l=n.exec(e),i=0,r=[];for(;l;)r.push(i0(e.slice(i,l.index),i>0,!0),l[0]),i=l.index+l[0].length,l=n.exec(e);return r.push(i0(e.slice(i),i>0,!1)),r.join("")}function i0(t,e,n){let l=0,i=t.length;if(e){let r=t.codePointAt(l);for(;r===9||r===32;)l++,r=t.codePointAt(l)}if(n){let r=t.codePointAt(i-1);for(;r===9||r===32;)i--,r=t.codePointAt(i-1)}return i>l?t.slice(l,i):""}function a0(t,e){let n={type:"text",value:r0(String(e.value))};return t.patch(e,n),t.applyData(e,n)}function u0(t,e){let n={type:"element",tagName:"hr",properties:{},children:[]};return t.patch(e,n),t.applyData(e,n)}var o0={blockquote:U1,break:B1,code:H1,delete:q1,emphasis:j1,footnoteReference:Y1,heading:V1,html:X1,imageReference:G1,image:Q1,inlineCode:Z1,linkReference:F1,link:K1,listItem:I1,list:W1,paragraph:P1,root:$1,strong:t0,table:e0,tableCell:l0,tableRow:n0,text:a0,thematicBreak:u0,toml:fo,yaml:fo,definition:fo,footnoteDefinition:fo};function fo(){}var c0=typeof self=="object"?self:globalThis,c2=(t,e)=>{let n=(i,r)=>(t.set(r,i),i),l=i=>{if(t.has(i))return t.get(i);let[r,a]=e[i];switch(r){case 0:case-1:return n(a,i);case 1:{let u=n([],i);for(let o of a)u.push(l(o));return u}case 2:{let u=n({},i);for(let[o,c]of a)u[l(o)]=l(c);return u}case 3:return n(new Date(a),i);case 4:{let{source:u,flags:o}=a;return n(new RegExp(u,o),i)}case 5:{let u=n(new Map,i);for(let[o,c]of a)u.set(l(o),l(c));return u}case 6:{let u=n(new Set,i);for(let o of a)u.add(l(o));return u}case 7:{let{name:u,message:o}=a;return n(new c0[u](o),i)}case 8:return n(BigInt(a),i);case"BigInt":return n(Object(BigInt(a)),i);case"ArrayBuffer":return n(new Uint8Array(a).buffer,a);case"DataView":{let{buffer:u}=new Uint8Array(a);return n(new DataView(u),a)}}return n(new c0[r](a),i)};return l},Zf=t=>c2(new Map,t)(0);var Ni="",{toString:s2}={},{keys:f2}=Object,oa=t=>{let e=typeof t;if(e!=="object"||!t)return[0,e];let n=s2.call(t).slice(8,-1);switch(n){case"Array":return[1,Ni];case"Object":return[2,Ni];case"Date":return[3,Ni];case"RegExp":return[4,Ni];case"Map":return[5,Ni];case"Set":return[6,Ni];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},po=([t,e])=>t===0&&(e==="function"||e==="symbol"),m2=(t,e,n,l)=>{let i=(a,u)=>{let o=l.push(a)-1;return n.set(u,o),o},r=a=>{if(n.has(a))return n.get(a);let[u,o]=oa(a);switch(u){case 0:{let s=a;switch(o){case"bigint":u=8,s=a.toString();break;case"function":case"symbol":if(t)throw new TypeError("unable to serialize "+o);s=null;break;case"undefined":return i([-1],a)}return i([u,s],a)}case 1:{if(o){let p=a;return o==="DataView"?p=new Uint8Array(a.buffer):o==="ArrayBuffer"&&(p=new Uint8Array(a)),i([o,[...p]],a)}let s=[],f=i([u,s],a);for(let p of a)s.push(r(p));return f}case 2:{if(o)switch(o){case"BigInt":return i([o,a.toString()],a);case"Boolean":case"Number":case"String":return i([o,a.valueOf()],a)}if(e&&"toJSON"in a)return r(a.toJSON());let s=[],f=i([u,s],a);for(let p of f2(a))(t||!po(oa(a[p])))&&s.push([r(p),r(a[p])]);return f}case 3:return i([u,a.toISOString()],a);case 4:{let{source:s,flags:f}=a;return i([u,{source:s,flags:f}],a)}case 5:{let s=[],f=i([u,s],a);for(let[p,m]of a)(t||!(po(oa(p))||po(oa(m))))&&s.push([r(p),r(m)]);return f}case 6:{let s=[],f=i([u,s],a);for(let p of a)(t||!po(oa(p)))&&s.push(r(p));return f}}let{message:c}=a;return i([u,{name:o,message:c}],a)};return r},Ff=(t,{json:e,lossy:n}={})=>{let l=[];return m2(!(e||n),!!e,new Map,l)(t),l};var Li=typeof structuredClone=="function"?(t,e)=>e&&("json"in e||"lossy"in e)?Zf(Ff(t,e)):structuredClone(t):(t,e)=>Zf(Ff(t,e));function p2(t,e){let n=[{type:"text",value:"\u21A9"}];return e>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(e)}]}),n}function h2(t,e){return"Back to reference "+(t+1)+(e>1?"-"+e:"")}function h0(t){let e=typeof t.options.clobberPrefix=="string"?t.options.clobberPrefix:"user-content-",n=t.options.footnoteBackContent||p2,l=t.options.footnoteBackLabel||h2,i=t.options.footnoteLabel||"Footnotes",r=t.options.footnoteLabelTagName||"h2",a=t.options.footnoteLabelProperties||{className:["sr-only"]},u=[],o=-1;for(;++o<t.footnoteOrder.length;){let c=t.footnoteById.get(t.footnoteOrder[o]);if(!c)continue;let s=t.all(c),f=String(c.identifier).toUpperCase(),p=Oe(f.toLowerCase()),m=0,y=[],v=t.footnoteCounts.get(f);for(;v!==void 0&&++m<=v;){y.length>0&&y.push({type:"text",value:" "});let d=typeof n=="string"?n:n(o,m);typeof d=="string"&&(d={type:"text",value:d}),y.push({type:"element",tagName:"a",properties:{href:"#"+e+"fnref-"+p+(m>1?"-"+m:""),dataFootnoteBackref:"",ariaLabel:typeof l=="string"?l:l(o,m),className:["data-footnote-backref"]},children:Array.isArray(d)?d:[d]})}let T=s[s.length-1];if(T&&T.type==="element"&&T.tagName==="p"){let d=T.children[T.children.length-1];d&&d.type==="text"?d.value+=" ":T.children.push({type:"text",value:" "}),T.children.push(...y)}else s.push(...y);let h={type:"element",tagName:"li",properties:{id:e+"fn-"+p},children:t.wrap(s,!0)};t.patch(c,h),u.push(h)}if(u.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:r,properties:Nt(C({},Li(a)),{id:"footnote-label"}),children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:t.wrap(u,!0)},{type:"text",value:`
`}]}}var $n=function(t){if(t==null)return x2;if(typeof t=="function")return ho(t);if(typeof t=="object")return Array.isArray(t)?d2(t):g2(t);if(typeof t=="string")return y2(t);throw new Error("Expected function, string, or object as test")};function d2(t){let e=[],n=-1;for(;++n<t.length;)e[n]=$n(t[n]);return ho(l);function l(...i){let r=-1;for(;++r<e.length;)if(e[r].apply(this,i))return!0;return!1}}function g2(t){let e=t;return ho(n);function n(l){let i=l,r;for(r in t)if(i[r]!==e[r])return!1;return!0}}function y2(t){return ho(e);function e(n){return n&&n.type===t}}function ho(t){return e;function e(n,l,i){return!!(b2(n)&&t.call(this,n,typeof l=="number"?l:void 0,i||void 0))}}function x2(){return!0}function b2(t){return t!==null&&typeof t=="object"&&"type"in t}var d0=[],go=!0,_l=!1,yo="skip";function ca(t,e,n,l){let i;typeof e=="function"&&typeof n!="function"?(l=n,n=e):i=e;let r=$n(i),a=l?-1:1;u(t,void 0,[])();function u(o,c,s){let f=o&&typeof o=="object"?o:{};if(typeof f.type=="string"){let m=typeof f.tagName=="string"?f.tagName:typeof f.name=="string"?f.name:void 0;Object.defineProperty(p,"name",{value:"node ("+(o.type+(m?"<"+m+">":""))+")"})}return p;function p(){let m=d0,y,v,T;if((!e||r(o,c,s[s.length-1]||void 0))&&(m=v2(n(o,s)),m[0]===_l))return m;if("children"in o&&o.children){let h=o;if(h.children&&m[0]!==yo)for(v=(l?h.children.length:-1)+a,T=s.concat(h);v>-1&&v<h.children.length;){let d=h.children[v];if(y=u(d,v,T)(),y[0]===_l)return y;v=typeof y[1]=="number"?y[1]:v+a}}return m}}}function v2(t){return Array.isArray(t)?t:typeof t=="number"?[go,t]:t==null?d0:[t]}function Rl(t,e,n,l){let i,r,a;typeof e=="function"&&typeof n!="function"?(r=void 0,a=e,i=n):(r=e,a=n,i=l),ca(t,r,u,i);function u(o,c){let s=c[c.length-1],f=s?s.children.indexOf(o):void 0;return a(o,f,s)}}var Kf={}.hasOwnProperty,S2={};function y0(t,e){let n=e||S2,l=new Map,i=new Map,r=new Map,a=C(C({},o0),n.handlers),u={all:c,applyData:E2,definitionById:l,footnoteById:i,footnoteCounts:r,footnoteOrder:[],handlers:a,one:o,options:n,patch:k2,wrap:A2};return Rl(t,function(s){if(s.type==="definition"||s.type==="footnoteDefinition"){let f=s.type==="definition"?l:i,p=String(s.identifier).toUpperCase();f.has(p)||f.set(p,s)}}),u;function o(s,f){let p=s.type,m=u.handlers[p];if(Kf.call(u.handlers,p)&&m)return m(u,s,f);if(u.options.passThrough&&u.options.passThrough.includes(p)){if("children"in s){let v=s,{children:T}=v,h=Pm(v,["children"]),d=Li(h);return d.children=u.all(s),d}return Li(s)}return(u.options.unknownHandler||T2)(u,s,f)}function c(s){let f=[];if("children"in s){let p=s.children,m=-1;for(;++m<p.length;){let y=u.one(p[m],s);if(y){if(m&&p[m-1].type==="break"&&(!Array.isArray(y)&&y.type==="text"&&(y.value=g0(y.value)),!Array.isArray(y)&&y.type==="element")){let v=y.children[0];v&&v.type==="text"&&(v.value=g0(v.value))}Array.isArray(y)?f.push(...y):f.push(y)}}}return f}}function k2(t,e){t.position&&(e.position=kf(t))}function E2(t,e){let n=e;if(t&&t.data){let l=t.data.hName,i=t.data.hChildren,r=t.data.hProperties;if(typeof l=="string")if(n.type==="element")n.tagName=l;else{let a="children"in n?n.children:[n];n={type:"element",tagName:l,properties:{},children:a}}n.type==="element"&&r&&Object.assign(n.properties,Li(r)),"children"in n&&n.children&&i!==null&&i!==void 0&&(n.children=i)}return n}function T2(t,e){let n=e.data||{},l="value"in e&&!(Kf.call(n,"hProperties")||Kf.call(n,"hChildren"))?{type:"text",value:e.value}:{type:"element",tagName:"div",properties:{},children:t.all(e)};return t.patch(e,l),t.applyData(e,l)}function A2(t,e){let n=[],l=-1;for(e&&n.push({type:"text",value:`
`});++l<t.length;)l&&n.push({type:"text",value:`
`}),n.push(t[l]);return e&&t.length>0&&n.push({type:"text",value:`
`}),n}function g0(t){let e=0,n=t.charCodeAt(e);for(;n===9||n===32;)e++,n=t.charCodeAt(e);return t.slice(e)}function xo(t,e){let n=y0(t,e),l=n.one(t,void 0),i=h0(n),r=Array.isArray(l)?{type:"root",children:l}:l||{type:"root",children:[]};return i&&("children"in r,r.children.push({type:"text",value:`
`},i)),r}function bo(t,e){return t&&"run"in t?function(n,l){return Hl(this,null,function*(){let i=xo(n,C({file:l},e));yield t.run(i,l)})}:function(n,l){return xo(n,C({file:l},t||e))}}function If(t){if(t)throw t}var ko=vn(w0(),1);function sa(t){if(typeof t!="object"||t===null)return!1;let e=Object.getPrototypeOf(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)}function Jf(){let t=[],e={run:n,use:l};return e;function n(...i){let r=-1,a=i.pop();if(typeof a!="function")throw new TypeError("Expected function as last argument, not "+a);u(null,...i);function u(o,...c){let s=t[++r],f=-1;if(o){a(o);return}for(;++f<i.length;)(c[f]===null||c[f]===void 0)&&(c[f]=i[f]);i=c,s?z0(s,u)(...c):a(null,...c)}}function l(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return t.push(i),e}}function z0(t,e){let n;return l;function l(...a){let u=t.length>a.length,o;u&&a.push(i);try{o=t.apply(this,a)}catch(c){let s=c;if(u&&n)throw s;return i(s)}u||(o&&o.then&&typeof o.then=="function"?o.then(r,i):o instanceof Error?i(o):r(o))}function i(a,...u){n||(n=!0,e(a,...u))}function r(a){i(null,a)}}var Be={basename:w2,dirname:z2,extname:C2,join:D2,sep:"/"};function w2(t,e){if(e!==void 0&&typeof e!="string")throw new TypeError('"ext" argument must be a string');fa(t);let n=0,l=-1,i=t.length,r;if(e===void 0||e.length===0||e.length>t.length){for(;i--;)if(t.codePointAt(i)===47){if(r){n=i+1;break}}else l<0&&(r=!0,l=i+1);return l<0?"":t.slice(n,l)}if(e===t)return"";let a=-1,u=e.length-1;for(;i--;)if(t.codePointAt(i)===47){if(r){n=i+1;break}}else a<0&&(r=!0,a=i+1),u>-1&&(t.codePointAt(i)===e.codePointAt(u--)?u<0&&(l=i):(u=-1,l=a));return n===l?l=a:l<0&&(l=t.length),t.slice(n,l)}function z2(t){if(fa(t),t.length===0)return".";let e=-1,n=t.length,l;for(;--n;)if(t.codePointAt(n)===47){if(l){e=n;break}}else l||(l=!0);return e<0?t.codePointAt(0)===47?"/":".":e===1&&t.codePointAt(0)===47?"//":t.slice(0,e)}function C2(t){fa(t);let e=t.length,n=-1,l=0,i=-1,r=0,a;for(;e--;){let u=t.codePointAt(e);if(u===47){if(a){l=e+1;break}continue}n<0&&(a=!0,n=e+1),u===46?i<0?i=e:r!==1&&(r=1):i>-1&&(r=-1)}return i<0||n<0||r===0||r===1&&i===n-1&&i===l+1?"":t.slice(i,n)}function D2(...t){let e=-1,n;for(;++e<t.length;)fa(t[e]),t[e]&&(n=n===void 0?t[e]:n+"/"+t[e]);return n===void 0?".":M2(n)}function M2(t){fa(t);let e=t.codePointAt(0)===47,n=O2(t,!e);return n.length===0&&!e&&(n="."),n.length>0&&t.codePointAt(t.length-1)===47&&(n+="/"),e?"/"+n:n}function O2(t,e){let n="",l=0,i=-1,r=0,a=-1,u,o;for(;++a<=t.length;){if(a<t.length)u=t.codePointAt(a);else{if(u===47)break;u=47}if(u===47){if(!(i===a-1||r===1))if(i!==a-1&&r===2){if(n.length<2||l!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(o=n.lastIndexOf("/"),o!==n.length-1){o<0?(n="",l=0):(n=n.slice(0,o),l=n.length-1-n.lastIndexOf("/")),i=a,r=0;continue}}else if(n.length>0){n="",l=0,i=a,r=0;continue}}e&&(n=n.length>0?n+"/..":"..",l=2)}else n.length>0?n+="/"+t.slice(i+1,a):n=t.slice(i+1,a),l=a-i-1;i=a,r=0}else u===46&&r>-1?r++:r=-1}return n}function fa(t){if(typeof t!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}var C0={cwd:_2};function _2(){return"/"}function Ui(t){return!!(t!==null&&typeof t=="object"&&"href"in t&&t.href&&"protocol"in t&&t.protocol&&t.auth===void 0)}function D0(t){if(typeof t=="string")t=new URL(t);else if(!Ui(t)){let e=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+t+"`");throw e.code="ERR_INVALID_ARG_TYPE",e}if(t.protocol!=="file:"){let e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return R2(t)}function R2(t){if(t.hostname!==""){let l=new TypeError('File URL host must be "localhost" or empty on darwin');throw l.code="ERR_INVALID_FILE_URL_HOST",l}let e=t.pathname,n=-1;for(;++n<e.length;)if(e.codePointAt(n)===37&&e.codePointAt(n+1)===50){let l=e.codePointAt(n+2);if(l===70||l===102){let i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(e)}var Wf=["history","path","basename","stem","extname","dirname"],Nl=class{constructor(e){let n;e?Ui(e)?n={path:e}:typeof e=="string"||N2(e)?n={value:e}:n=e:n={},this.cwd="cwd"in n?"":C0.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let l=-1;for(;++l<Wf.length;){let r=Wf[l];r in n&&n[r]!==void 0&&n[r]!==null&&(this[r]=r==="history"?[...n[r]]:n[r])}let i;for(i in n)Wf.includes(i)||(this[i]=n[i])}get basename(){return typeof this.path=="string"?Be.basename(this.path):void 0}set basename(e){$f(e,"basename"),Pf(e,"basename"),this.path=Be.join(this.dirname||"",e)}get dirname(){return typeof this.path=="string"?Be.dirname(this.path):void 0}set dirname(e){M0(this.basename,"dirname"),this.path=Be.join(e||"",this.basename)}get extname(){return typeof this.path=="string"?Be.extname(this.path):void 0}set extname(e){if(Pf(e,"extname"),M0(this.dirname,"extname"),e){if(e.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Be.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){Ui(e)&&(e=D0(e)),$f(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return typeof this.path=="string"?Be.basename(this.path,this.extname):void 0}set stem(e){$f(e,"stem"),Pf(e,"stem"),this.path=Be.join(this.dirname||"",e+(this.extname||""))}fail(e,n,l){let i=this.message(e,n,l);throw i.fatal=!0,i}info(e,n,l){let i=this.message(e,n,l);return i.fatal=void 0,i}message(e,n,l){let i=new Tt(e,n,l);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(e){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(e||void 0).decode(this.value)}};function Pf(t,e){if(t&&t.includes(Be.sep))throw new Error("`"+e+"` cannot be a path: did not expect `"+Be.sep+"`")}function $f(t,e){if(!t)throw new Error("`"+e+"` cannot be empty")}function M0(t,e){if(!t)throw new Error("Setting `"+e+"` requires `path` to be set too")}function N2(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var O0=function(t){let l=this.constructor.prototype,i=l[t],r=function(){return i.apply(r,arguments)};return Object.setPrototypeOf(r,l),r};var L2={}.hasOwnProperty,lm=class t extends O0{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Jf()}copy(){let e=new t,n=-1;for(;++n<this.attachers.length;){let l=this.attachers[n];e.use(...l)}return e.data((0,ko.default)(!0,{},this.namespace)),e}data(e,n){return typeof e=="string"?arguments.length===2?(nm("data",this.frozen),this.namespace[e]=n,this):L2.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nm("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;let e=this;for(;++this.freezeIndex<this.attachers.length;){let[n,...l]=this.attachers[this.freezeIndex];if(l[0]===!1)continue;l[0]===!0&&(l[0]=void 0);let i=n.call(e,...l);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let n=So(e),l=this.parser||this.Parser;return tm("parse",l),l(String(n),n)}process(e,n){let l=this;return this.freeze(),tm("process",this.parser||this.Parser),em("process",this.compiler||this.Compiler),n?i(void 0,n):new Promise(i);function i(r,a){let u=So(e),o=l.parse(u);l.run(o,u,function(s,f,p){if(s||!f||!p)return c(s);let m=f,y=l.stringify(m,p);B2(y)?p.value=y:p.result=y,c(s,p)});function c(s,f){s||!f?a(s):r?r(f):n(void 0,f)}}}processSync(e){let n=!1,l;return this.freeze(),tm("processSync",this.parser||this.Parser),em("processSync",this.compiler||this.Compiler),this.process(e,i),R0("processSync","process",n),l;function i(r,a){n=!0,If(r),l=a}}run(e,n,l){_0(e),this.freeze();let i=this.transformers;return!l&&typeof n=="function"&&(l=n,n=void 0),l?r(void 0,l):new Promise(r);function r(a,u){let o=So(n);i.run(e,o,c);function c(s,f,p){let m=f||e;s?u(s):a?a(m):l(void 0,m,p)}}}runSync(e,n){let l=!1,i;return this.run(e,n,r),R0("runSync","run",l),i;function r(a,u){If(a),i=u,l=!0}}stringify(e,n){this.freeze();let l=So(n),i=this.compiler||this.Compiler;return em("stringify",i),_0(e),i(e,l)}use(e,...n){let l=this.attachers,i=this.namespace;if(nm("use",this.frozen),e!=null)if(typeof e=="function")o(e,n);else if(typeof e=="object")Array.isArray(e)?u(e):a(e);else throw new TypeError("Expected usable value, not `"+e+"`");return this;function r(c){if(typeof c=="function")o(c,[]);else if(typeof c=="object")if(Array.isArray(c)){let[s,...f]=c;o(s,f)}else a(c);else throw new TypeError("Expected usable value, not `"+c+"`")}function a(c){if(!("plugins"in c)&&!("settings"in c))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");u(c.plugins),c.settings&&(i.settings=(0,ko.default)(!0,i.settings,c.settings))}function u(c){let s=-1;if(c!=null)if(Array.isArray(c))for(;++s<c.length;){let f=c[s];r(f)}else throw new TypeError("Expected a list of plugins, not `"+c+"`")}function o(c,s){let f=-1,p=-1;for(;++f<l.length;)if(l[f][0]===c){p=f;break}if(p===-1)l.push([c,...s]);else if(s.length>0){let[m,...y]=s,v=l[p][1];sa(v)&&sa(m)&&(m=(0,ko.default)(!0,v,m)),l[p]=[c,m,...y]}}}},im=new lm().freeze();function tm(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `parser`")}function em(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `compiler`")}function nm(t,e){if(e)throw new Error("Cannot call `"+t+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function _0(t){if(!sa(t)||typeof t.type!="string")throw new TypeError("Expected node, got `"+t+"`")}function R0(t,e,n){if(!n)throw new Error("`"+t+"` finished async. Use `"+e+"` instead")}function So(t){return U2(t)?t:new Nl(t)}function U2(t){return!!(t&&typeof t=="object"&&"message"in t&&"messages"in t)}function B2(t){return typeof t=="string"||H2(t)}function H2(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var q2="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",N0=[],L0={allowDangerousHtml:!0},j2=/^(https?|ircs?|mailto|xmpp)$/i,Y2=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function rm(t){let e=V2(t),n=X2(t);return G2(e.runSync(e.parse(n),n),t)}function V2(t){let e=t.rehypePlugins||N0,n=t.remarkPlugins||N0,l=t.remarkRehypeOptions?C(C({},t.remarkRehypeOptions),L0):L0;return im().use(co).use(n).use(bo,l).use(e)}function X2(t){let e=t.children||"",n=new Nl;return typeof e=="string"?n.value=e:(""+e,void 0),n}function G2(t,e){let n=e.allowedElements,l=e.allowElement,i=e.components,r=e.disallowedElements,a=e.skipHtml,u=e.unwrapDisallowed,o=e.urlTransform||B0;for(let s of Y2)Object.hasOwn(e,s.from)&&(""+s.from+(s.to?"use `"+s.to+"` instead":"remove it")+q2+s.id,void 0);return n&&r&&void 0,Rl(t,c),Af(t,{Fragment:Bi.Fragment,components:i,ignoreInvalidStyle:!0,jsx:Bi.jsx,jsxs:Bi.jsxs,passKeys:!0,passNode:!0});function c(s,f,p){if(s.type==="raw"&&p&&typeof f=="number")return a?p.children.splice(f,1):p.children[f]={type:"text",value:s.value},f;if(s.type==="element"){let m;for(m in na)if(Object.hasOwn(na,m)&&Object.hasOwn(s.properties,m)){let y=s.properties[m],v=na[m];(v===null||v.includes(s.tagName))&&(s.properties[m]=o(String(y||""),m,s))}}if(s.type==="element"){let m=n?!n.includes(s.tagName):r?r.includes(s.tagName):!1;if(!m&&l&&typeof f=="number"&&(m=!l(s,f,p)),m&&p&&typeof f=="number")return u&&s.children?p.children.splice(f,1,...s.children):p.children.splice(f,1),f}}}function B0(t){let e=t.indexOf(":"),n=t.indexOf("?"),l=t.indexOf("#"),i=t.indexOf("/");return e===-1||i!==-1&&e>i||n!==-1&&e>n||l!==-1&&e>l||j2.test(t.slice(0,e))?t:""}function am(t,e){let n=String(t);if(typeof e!="string")throw new TypeError("Expected character");let l=0,i=n.indexOf(e);for(;i!==-1;)l++,i=n.indexOf(e,i+e.length);return l}function um(t){if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function om(t,e,n){let i=$n((n||{}).ignore||[]),r=Q2(e),a=-1;for(;++a<r.length;)ca(t,"text",u);function u(c,s){let f=-1,p;for(;++f<s.length;){let m=s[f],y=p?p.children:void 0;if(i(m,y?y.indexOf(m):void 0,p))return;p=m}if(p)return o(c,s)}function o(c,s){let f=s[s.length-1],p=r[a][0],m=r[a][1],y=0,T=f.children.indexOf(c),h=!1,d=[];p.lastIndex=0;let g=p.exec(c.value);for(;g;){let k=g.index,z={index:g.index,input:g.input,stack:[...s,c]},E=m(...g,z);if(typeof E=="string"&&(E=E.length>0?{type:"text",value:E}:void 0),E===!1?p.lastIndex=k+1:(y!==k&&d.push({type:"text",value:c.value.slice(y,k)}),Array.isArray(E)?d.push(...E):E&&d.push(E),y=k+g[0].length,h=!0),!p.global)break;g=p.exec(c.value)}return h?(y<c.value.length&&d.push({type:"text",value:c.value.slice(y)}),f.children.splice(T,1,...d)):d=[c],T+d.length}}function Q2(t){let e=[];if(!Array.isArray(t))throw new TypeError("Expected find and replace tuple or list of tuples");let n=!t[0]||Array.isArray(t[0])?t:[t],l=-1;for(;++l<n.length;){let i=n[l];e.push([Z2(i[0]),F2(i[1])])}return e}function Z2(t){return typeof t=="string"?new RegExp(um(t),"g"):t}function F2(t){return typeof t=="function"?t:function(){return t}}var cm="phrasing",sm=["autolink","link","image","label"];function mm(){return{transforms:[$2],enter:{literalAutolink:K2,literalAutolinkEmail:fm,literalAutolinkHttp:fm,literalAutolinkWww:fm},exit:{literalAutolink:P2,literalAutolinkEmail:W2,literalAutolinkHttp:I2,literalAutolinkWww:J2}}}function pm(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:cm,notInConstruct:sm},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:cm,notInConstruct:sm},{character:":",before:"[ps]",after:"\\/",inConstruct:cm,notInConstruct:sm}]}}function K2(t){this.enter({type:"link",title:null,url:"",children:[]},t)}function fm(t){this.config.enter.autolinkProtocol.call(this,t)}function I2(t){this.config.exit.autolinkProtocol.call(this,t)}function J2(t){this.config.exit.data.call(this,t);let e=this.stack[this.stack.length-1];e.type,e.url="http://"+this.sliceSerialize(t)}function W2(t){this.config.exit.autolinkEmail.call(this,t)}function P2(t){this.exit(t)}function $2(t){om(t,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,tT],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),eT]],{ignore:["link","linkReference"]})}function tT(t,e,n,l,i){let r="";if(!H0(i)||(/^w/i.test(e)&&(n=e+n,e="",r="http://"),!nT(n)))return!1;let a=lT(n+l);if(!a[0])return!1;let u={type:"link",title:null,url:r+e+a[0],children:[{type:"text",value:e+a[0]}]};return a[1]?[u,{type:"text",value:a[1]}]:u}function eT(t,e,n,l){return!H0(l,!0)||/[-\d_]$/.test(n)?!1:{type:"link",title:null,url:"mailto:"+e+"@"+n,children:[{type:"text",value:e+"@"+n}]}}function nT(t){let e=t.split(".");return!(e.length<2||e[e.length-1]&&(/_/.test(e[e.length-1])||!/[a-zA-Z\d]/.test(e[e.length-1]))||e[e.length-2]&&(/_/.test(e[e.length-2])||!/[a-zA-Z\d]/.test(e[e.length-2])))}function lT(t){let e=/[!"&'),.:;<>?\]}]+$/.exec(t);if(!e)return[t,void 0];t=t.slice(0,e.index);let n=e[0],l=n.indexOf(")"),i=am(t,"("),r=am(t,")");for(;l!==-1&&i>r;)t+=n.slice(0,l+1),n=n.slice(l+1),l=n.indexOf(")"),r++;return[t,n]}function H0(t,e){let n=t.input.charCodeAt(t.index-1);return(t.index===0||Fe(n)||Cl(n))&&(!e||n!==47)}q0.peek=mT;function iT(){this.buffer()}function rT(t){this.enter({type:"footnoteReference",identifier:"",label:""},t)}function aT(){this.buffer()}function uT(t){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},t)}function oT(t){let e=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=Jt(this.sliceSerialize(t)).toLowerCase(),n.label=e}function cT(t){this.exit(t)}function sT(t){let e=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=Jt(this.sliceSerialize(t)).toLowerCase(),n.label=e}function fT(t){this.exit(t)}function mT(){return"["}function q0(t,e,n,l){let i=n.createTracker(l),r=i.move("[^"),a=n.enter("footnoteReference"),u=n.enter("reference");return r+=i.move(n.safe(n.associationId(t),{after:"]",before:r})),u(),a(),r+=i.move("]"),r}function hm(){return{enter:{gfmFootnoteCallString:iT,gfmFootnoteCall:rT,gfmFootnoteDefinitionLabelString:aT,gfmFootnoteDefinition:uT},exit:{gfmFootnoteCallString:oT,gfmFootnoteCall:cT,gfmFootnoteDefinitionLabelString:sT,gfmFootnoteDefinition:fT}}}function dm(t){let e=!1;return t&&t.firstLineBlank&&(e=!0),{handlers:{footnoteDefinition:n,footnoteReference:q0},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function n(l,i,r,a){let u=r.createTracker(a),o=u.move("[^"),c=r.enter("footnoteDefinition"),s=r.enter("label");return o+=u.move(r.safe(r.associationId(l),{before:o,after:"]"})),s(),o+=u.move("]:"),l.children&&l.children.length>0&&(u.shift(4),o+=u.move((e?`
`:" ")+r.indentLines(r.containerFlow(l,u.current()),e?j0:pT))),c(),o}}function pT(t,e,n){return e===0?t:j0(t,e,n)}function j0(t,e,n){return(n?"":"    ")+t}var hT=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];Y0.peek=yT;function gm(){return{canContainEols:["delete"],enter:{strikethrough:dT},exit:{strikethrough:gT}}}function ym(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:hT}],handlers:{delete:Y0}}}function dT(t){this.enter({type:"delete",children:[]},t)}function gT(t){this.exit(t)}function Y0(t,e,n,l){let i=n.createTracker(l),r=n.enter("strikethrough"),a=i.move("~~");return a+=n.containerPhrasing(t,Nt(C({},i.current()),{before:a,after:"~"})),a+=i.move("~~"),r(),a}function yT(){return"~"}function xT(t){return t.length}function X0(t,e){let n=e||{},l=(n.align||[]).concat(),i=n.stringLength||xT,r=[],a=[],u=[],o=[],c=0,s=-1;for(;++s<t.length;){let v=[],T=[],h=-1;for(t[s].length>c&&(c=t[s].length);++h<t[s].length;){let d=bT(t[s][h]);if(n.alignDelimiters!==!1){let g=i(d);T[h]=g,(o[h]===void 0||g>o[h])&&(o[h]=g)}v.push(d)}a[s]=v,u[s]=T}let f=-1;if(typeof l=="object"&&"length"in l)for(;++f<c;)r[f]=V0(l[f]);else{let v=V0(l);for(;++f<c;)r[f]=v}f=-1;let p=[],m=[];for(;++f<c;){let v=r[f],T="",h="";v===99?(T=":",h=":"):v===108?T=":":v===114&&(h=":");let d=n.alignDelimiters===!1?1:Math.max(1,o[f]-T.length-h.length),g=T+"-".repeat(d)+h;n.alignDelimiters!==!1&&(d=T.length+d+h.length,d>o[f]&&(o[f]=d),m[f]=d),p[f]=g}a.splice(1,0,p),u.splice(1,0,m),s=-1;let y=[];for(;++s<a.length;){let v=a[s],T=u[s];f=-1;let h=[];for(;++f<c;){let d=v[f]||"",g="",k="";if(n.alignDelimiters!==!1){let z=o[f]-(T[f]||0),E=r[f];E===114?g=" ".repeat(z):E===99?z%2?(g=" ".repeat(z/2+.5),k=" ".repeat(z/2-.5)):(g=" ".repeat(z/2),k=g):k=" ".repeat(z)}n.delimiterStart!==!1&&!f&&h.push("|"),n.padding!==!1&&!(n.alignDelimiters===!1&&d==="")&&(n.delimiterStart!==!1||f)&&h.push(" "),n.alignDelimiters!==!1&&h.push(g),h.push(d),n.alignDelimiters!==!1&&h.push(k),n.padding!==!1&&h.push(" "),(n.delimiterEnd!==!1||f!==c-1)&&h.push("|")}y.push(n.delimiterEnd===!1?h.join("").replace(/ +$/,""):h.join(""))}return y.join(`
`)}function bT(t){return t==null?"":String(t)}function V0(t){let e=typeof t=="string"?t.codePointAt(0):0;return e===67||e===99?99:e===76||e===108?108:e===82||e===114?114:0}function G0(t,e,n,l){let i=n.enter("blockquote"),r=n.createTracker(l);r.move("> "),r.shift(2);let a=n.indentLines(n.containerFlow(t,r.current()),vT);return i(),a}function vT(t,e,n){return">"+(n?"":" ")+t}function Z0(t,e){return Q0(t,e.inConstruct,!0)&&!Q0(t,e.notInConstruct,!1)}function Q0(t,e,n){if(typeof e=="string"&&(e=[e]),!e||e.length===0)return n;let l=-1;for(;++l<e.length;)if(t.includes(e[l]))return!0;return!1}function xm(t,e,n,l){let i=-1;for(;++i<n.unsafe.length;)if(n.unsafe[i].character===`
`&&Z0(n.stack,n.unsafe[i]))return/[ \t]/.test(l.before)?"":" ";return`\\
`}function F0(t,e){let n=String(t),l=n.indexOf(e),i=l,r=0,a=0;if(typeof e!="string")throw new TypeError("Expected substring");for(;l!==-1;)l===i?++r>a&&(a=r):r=1,i=l+e.length,l=n.indexOf(e,i);return a}function K0(t,e){return!!(e.options.fences===!1&&t.value&&!t.lang&&/[^ \r\n]/.test(t.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(t.value))}function I0(t){let e=t.options.fence||"`";if(e!=="`"&&e!=="~")throw new Error("Cannot serialize code with `"+e+"` for `options.fence`, expected `` ` `` or `~`");return e}function J0(t,e,n,l){let i=I0(n),r=t.value||"",a=i==="`"?"GraveAccent":"Tilde";if(K0(t,n)){let f=n.enter("codeIndented"),p=n.indentLines(r,ST);return f(),p}let u=n.createTracker(l),o=i.repeat(Math.max(F0(r,i)+1,3)),c=n.enter("codeFenced"),s=u.move(o);if(t.lang){let f=n.enter(`codeFencedLang${a}`);s+=u.move(n.safe(t.lang,C({before:s,after:" ",encode:["`"]},u.current()))),f()}if(t.lang&&t.meta){let f=n.enter(`codeFencedMeta${a}`);s+=u.move(" "),s+=u.move(n.safe(t.meta,C({before:s,after:`
`,encode:["`"]},u.current()))),f()}return s+=u.move(`
`),r&&(s+=u.move(r+`
`)),s+=u.move(o),c(),s}function ST(t,e,n){return(n?"":"    ")+t}function Hi(t){let e=t.options.quote||'"';if(e!=='"'&&e!=="'")throw new Error("Cannot serialize title with `"+e+"` for `options.quote`, expected `\"`, or `'`");return e}function W0(t,e,n,l){let i=Hi(n),r=i==='"'?"Quote":"Apostrophe",a=n.enter("definition"),u=n.enter("label"),o=n.createTracker(l),c=o.move("[");return c+=o.move(n.safe(n.associationId(t),C({before:c,after:"]"},o.current()))),c+=o.move("]: "),u(),!t.url||/[\0- \u007F]/.test(t.url)?(u=n.enter("destinationLiteral"),c+=o.move("<"),c+=o.move(n.safe(t.url,C({before:c,after:">"},o.current()))),c+=o.move(">")):(u=n.enter("destinationRaw"),c+=o.move(n.safe(t.url,C({before:c,after:t.title?" ":`
`},o.current())))),u(),t.title&&(u=n.enter(`title${r}`),c+=o.move(" "+i),c+=o.move(n.safe(t.title,C({before:c,after:i},o.current()))),c+=o.move(i),u()),a(),c}function P0(t){let e=t.options.emphasis||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize emphasis with `"+e+"` for `options.emphasis`, expected `*`, or `_`");return e}function tl(t){return"&#x"+t.toString(16).toUpperCase()+";"}function qi(t,e,n){let l=xn(t),i=xn(e);return l===void 0?i===void 0?n==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:l===1?i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}bm.peek=kT;function bm(t,e,n,l){let i=P0(n),r=n.enter("emphasis"),a=n.createTracker(l),u=a.move(i),o=a.move(n.containerPhrasing(t,C({after:i,before:u},a.current()))),c=o.charCodeAt(0),s=qi(l.before.charCodeAt(l.before.length-1),c,i);s.inside&&(o=tl(c)+o.slice(1));let f=o.charCodeAt(o.length-1),p=qi(l.after.charCodeAt(0),f,i);p.inside&&(o=o.slice(0,-1)+tl(f));let m=a.move(i);return r(),n.attentionEncodeSurroundingInfo={after:p.outside,before:s.outside},u+o+m}function kT(t,e,n){return n.options.emphasis||"*"}function $0(t,e){let n=!1;return Rl(t,function(l){if("value"in l&&/\r?\n|\r/.test(l.value)||l.type==="break")return n=!0,_l}),!!((!t.depth||t.depth<3)&&wl(t)&&(e.options.setext||n))}function tx(t,e,n,l){let i=Math.max(Math.min(6,t.depth||1),1),r=n.createTracker(l);if($0(t,n)){let s=n.enter("headingSetext"),f=n.enter("phrasing"),p=n.containerPhrasing(t,Nt(C({},r.current()),{before:`
`,after:`
`}));return f(),s(),p+`
`+(i===1?"=":"-").repeat(p.length-(Math.max(p.lastIndexOf("\r"),p.lastIndexOf(`
`))+1))}let a="#".repeat(i),u=n.enter("headingAtx"),o=n.enter("phrasing");r.move(a+" ");let c=n.containerPhrasing(t,C({before:"# ",after:`
`},r.current()));return/^[\t ]/.test(c)&&(c=tl(c.charCodeAt(0))+c.slice(1)),c=c?a+" "+c:a,n.options.closeAtx&&(c+=" "+a),o(),u(),c}vm.peek=ET;function vm(t){return t.value||""}function ET(){return"<"}Sm.peek=TT;function Sm(t,e,n,l){let i=Hi(n),r=i==='"'?"Quote":"Apostrophe",a=n.enter("image"),u=n.enter("label"),o=n.createTracker(l),c=o.move("![");return c+=o.move(n.safe(t.alt,C({before:c,after:"]"},o.current()))),c+=o.move("]("),u(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(u=n.enter("destinationLiteral"),c+=o.move("<"),c+=o.move(n.safe(t.url,C({before:c,after:">"},o.current()))),c+=o.move(">")):(u=n.enter("destinationRaw"),c+=o.move(n.safe(t.url,C({before:c,after:t.title?" ":")"},o.current())))),u(),t.title&&(u=n.enter(`title${r}`),c+=o.move(" "+i),c+=o.move(n.safe(t.title,C({before:c,after:i},o.current()))),c+=o.move(i),u()),c+=o.move(")"),a(),c}function TT(){return"!"}km.peek=AT;function km(t,e,n,l){let i=t.referenceType,r=n.enter("imageReference"),a=n.enter("label"),u=n.createTracker(l),o=u.move("!["),c=n.safe(t.alt,C({before:o,after:"]"},u.current()));o+=u.move(c+"]["),a();let s=n.stack;n.stack=[],a=n.enter("reference");let f=n.safe(n.associationId(t),C({before:o,after:"]"},u.current()));return a(),n.stack=s,r(),i==="full"||!c||c!==f?o+=u.move(f+"]"):i==="shortcut"?o=o.slice(0,-1):o+=u.move("]"),o}function AT(){return"!"}Em.peek=wT;function Em(t,e,n){let l=t.value||"",i="`",r=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(l);)i+="`";for(/[^ \r\n]/.test(l)&&(/^[ \r\n]/.test(l)&&/[ \r\n]$/.test(l)||/^`|`$/.test(l))&&(l=" "+l+" ");++r<n.unsafe.length;){let a=n.unsafe[r],u=n.compilePattern(a),o;if(a.atBreak)for(;o=u.exec(l);){let c=o.index;l.charCodeAt(c)===10&&l.charCodeAt(c-1)===13&&c--,l=l.slice(0,c)+" "+l.slice(o.index+1)}}return i+l+i}function wT(){return"`"}function Tm(t,e){let n=wl(t);return!!(!e.options.resourceLink&&t.url&&!t.title&&t.children&&t.children.length===1&&t.children[0].type==="text"&&(n===t.url||"mailto:"+n===t.url)&&/^[a-z][a-z+.-]+:/i.test(t.url)&&!/[\0- <>\u007F]/.test(t.url))}Am.peek=zT;function Am(t,e,n,l){let i=Hi(n),r=i==='"'?"Quote":"Apostrophe",a=n.createTracker(l),u,o;if(Tm(t,n)){let s=n.stack;n.stack=[],u=n.enter("autolink");let f=a.move("<");return f+=a.move(n.containerPhrasing(t,C({before:f,after:">"},a.current()))),f+=a.move(">"),u(),n.stack=s,f}u=n.enter("link"),o=n.enter("label");let c=a.move("[");return c+=a.move(n.containerPhrasing(t,C({before:c,after:"]("},a.current()))),c+=a.move("]("),o(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(o=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(t.url,C({before:c,after:">"},a.current()))),c+=a.move(">")):(o=n.enter("destinationRaw"),c+=a.move(n.safe(t.url,C({before:c,after:t.title?" ":")"},a.current())))),o(),t.title&&(o=n.enter(`title${r}`),c+=a.move(" "+i),c+=a.move(n.safe(t.title,C({before:c,after:i},a.current()))),c+=a.move(i),o()),c+=a.move(")"),u(),c}function zT(t,e,n){return Tm(t,n)?"<":"["}wm.peek=CT;function wm(t,e,n,l){let i=t.referenceType,r=n.enter("linkReference"),a=n.enter("label"),u=n.createTracker(l),o=u.move("["),c=n.containerPhrasing(t,C({before:o,after:"]"},u.current()));o+=u.move(c+"]["),a();let s=n.stack;n.stack=[],a=n.enter("reference");let f=n.safe(n.associationId(t),C({before:o,after:"]"},u.current()));return a(),n.stack=s,r(),i==="full"||!c||c!==f?o+=u.move(f+"]"):i==="shortcut"?o=o.slice(0,-1):o+=u.move("]"),o}function CT(){return"["}function ji(t){let e=t.options.bullet||"*";if(e!=="*"&&e!=="+"&&e!=="-")throw new Error("Cannot serialize items with `"+e+"` for `options.bullet`, expected `*`, `+`, or `-`");return e}function ex(t){let e=ji(t),n=t.options.bulletOther;if(!n)return e==="*"?"-":"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===e)throw new Error("Expected `bullet` (`"+e+"`) and `bulletOther` (`"+n+"`) to be different");return n}function nx(t){let e=t.options.bulletOrdered||".";if(e!=="."&&e!==")")throw new Error("Cannot serialize items with `"+e+"` for `options.bulletOrdered`, expected `.` or `)`");return e}function Eo(t){let e=t.options.rule||"*";if(e!=="*"&&e!=="-"&&e!=="_")throw new Error("Cannot serialize rules with `"+e+"` for `options.rule`, expected `*`, `-`, or `_`");return e}function lx(t,e,n,l){let i=n.enter("list"),r=n.bulletCurrent,a=t.ordered?nx(n):ji(n),u=t.ordered?a==="."?")":".":ex(n),o=e&&n.bulletLastUsed?a===n.bulletLastUsed:!1;if(!t.ordered){let s=t.children?t.children[0]:void 0;if((a==="*"||a==="-")&&s&&(!s.children||!s.children[0])&&n.stack[n.stack.length-1]==="list"&&n.stack[n.stack.length-2]==="listItem"&&n.stack[n.stack.length-3]==="list"&&n.stack[n.stack.length-4]==="listItem"&&n.indexStack[n.indexStack.length-1]===0&&n.indexStack[n.indexStack.length-2]===0&&n.indexStack[n.indexStack.length-3]===0&&(o=!0),Eo(n)===a&&s){let f=-1;for(;++f<t.children.length;){let p=t.children[f];if(p&&p.type==="listItem"&&p.children&&p.children[0]&&p.children[0].type==="thematicBreak"){o=!0;break}}}}o&&(a=u),n.bulletCurrent=a;let c=n.containerFlow(t,l);return n.bulletLastUsed=a,n.bulletCurrent=r,i(),c}function ix(t){let e=t.options.listItemIndent||"one";if(e!=="tab"&&e!=="one"&&e!=="mixed")throw new Error("Cannot serialize items with `"+e+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return e}function rx(t,e,n,l){let i=ix(n),r=n.bulletCurrent||ji(n);e&&e.type==="list"&&e.ordered&&(r=(typeof e.start=="number"&&e.start>-1?e.start:1)+(n.options.incrementListMarker===!1?0:e.children.indexOf(t))+r);let a=r.length+1;(i==="tab"||i==="mixed"&&(e&&e.type==="list"&&e.spread||t.spread))&&(a=Math.ceil(a/4)*4);let u=n.createTracker(l);u.move(r+" ".repeat(a-r.length)),u.shift(a);let o=n.enter("listItem"),c=n.indentLines(n.containerFlow(t,u.current()),s);return o(),c;function s(f,p,m){return p?(m?"":" ".repeat(a))+f:(m?r:r+" ".repeat(a-r.length))+f}}function ax(t,e,n,l){let i=n.enter("paragraph"),r=n.enter("phrasing"),a=n.containerPhrasing(t,l);return r(),i(),a}var zm=$n(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function ux(t,e,n,l){return(t.children.some(function(a){return zm(a)})?n.containerPhrasing:n.containerFlow).call(n,t,l)}function ox(t){let e=t.options.strong||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize strong with `"+e+"` for `options.strong`, expected `*`, or `_`");return e}Cm.peek=DT;function Cm(t,e,n,l){let i=ox(n),r=n.enter("strong"),a=n.createTracker(l),u=a.move(i+i),o=a.move(n.containerPhrasing(t,C({after:i,before:u},a.current()))),c=o.charCodeAt(0),s=qi(l.before.charCodeAt(l.before.length-1),c,i);s.inside&&(o=tl(c)+o.slice(1));let f=o.charCodeAt(o.length-1),p=qi(l.after.charCodeAt(0),f,i);p.inside&&(o=o.slice(0,-1)+tl(f));let m=a.move(i+i);return r(),n.attentionEncodeSurroundingInfo={after:p.outside,before:s.outside},u+o+m}function DT(t,e,n){return n.options.strong||"*"}function cx(t,e,n,l){return n.safe(t.value,l)}function sx(t){let e=t.options.ruleRepetition||3;if(e<3)throw new Error("Cannot serialize rules with repetition `"+e+"` for `options.ruleRepetition`, expected `3` or more");return e}function fx(t,e,n){let l=(Eo(n)+(n.options.ruleSpaces?" ":"")).repeat(sx(n));return n.options.ruleSpaces?l.slice(0,-1):l}var ma={blockquote:G0,break:xm,code:J0,definition:W0,emphasis:bm,hardBreak:xm,heading:tx,html:vm,image:Sm,imageReference:km,inlineCode:Em,link:Am,linkReference:wm,list:lx,listItem:rx,paragraph:ax,root:ux,strong:Cm,text:cx,thematicBreak:fx};function Mm(){return{enter:{table:MT,tableData:mx,tableHeader:mx,tableRow:_T},exit:{codeText:RT,table:OT,tableData:Dm,tableHeader:Dm,tableRow:Dm}}}function MT(t){let e=t._align;this.enter({type:"table",align:e.map(function(n){return n==="none"?null:n}),children:[]},t),this.data.inTable=!0}function OT(t){this.exit(t),this.data.inTable=void 0}function _T(t){this.enter({type:"tableRow",children:[]},t)}function Dm(t){this.exit(t)}function mx(t){this.enter({type:"tableCell",children:[]},t)}function RT(t){let e=this.resume();this.data.inTable&&(e=e.replace(/\\([\\|])/g,NT));let n=this.stack[this.stack.length-1];n.type,n.value=e,this.exit(t)}function NT(t,e){return e==="|"?e:t}function Om(t){let e=t||{},n=e.tableCellPadding,l=e.tablePipeAlign,i=e.stringLength,r=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:p,table:a,tableCell:o,tableRow:u}};function a(m,y,v,T){return c(s(m,v,T),m.align)}function u(m,y,v,T){let h=f(m,v,T),d=c([h]);return d.slice(0,d.indexOf(`
`))}function o(m,y,v,T){let h=v.enter("tableCell"),d=v.enter("phrasing"),g=v.containerPhrasing(m,Nt(C({},T),{before:r,after:r}));return d(),h(),g}function c(m,y){return X0(m,{align:y,alignDelimiters:l,padding:n,stringLength:i})}function s(m,y,v){let T=m.children,h=-1,d=[],g=y.enter("table");for(;++h<T.length;)d[h]=f(T[h],y,v);return g(),d}function f(m,y,v){let T=m.children,h=-1,d=[],g=y.enter("tableRow");for(;++h<T.length;)d[h]=o(T[h],m,y,v);return g(),d}function p(m,y,v){let T=ma.inlineCode(m,y,v);return v.stack.includes("tableCell")&&(T=T.replace(/\|/g,"\\$&")),T}}function _m(){return{exit:{taskListCheckValueChecked:px,taskListCheckValueUnchecked:px,paragraph:LT}}}function Rm(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:UT}}}function px(t){let e=this.stack[this.stack.length-2];e.type,e.checked=t.type==="taskListCheckValueChecked"}function LT(t){let e=this.stack[this.stack.length-2];if(e&&e.type==="listItem"&&typeof e.checked=="boolean"){let n=this.stack[this.stack.length-1];n.type;let l=n.children[0];if(l&&l.type==="text"){let i=e.children,r=-1,a;for(;++r<i.length;){let u=i[r];if(u.type==="paragraph"){a=u;break}}a===n&&(l.value=l.value.slice(1),l.value.length===0?n.children.shift():n.position&&l.position&&typeof l.position.start.offset=="number"&&(l.position.start.column++,l.position.start.offset++,n.position.start=Object.assign({},l.position.start)))}}this.exit(t)}function UT(t,e,n,l){let i=t.children[0],r=typeof t.checked=="boolean"&&i&&i.type==="paragraph",a="["+(t.checked?"x":" ")+"] ",u=n.createTracker(l);r&&u.move(a);let o=ma.listItem(t,e,n,C(C({},l),u.current()));return r&&(o=o.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,c)),o;function c(s){return s+a}}function Nm(){return[mm(),hm(),gm(),Mm(),_m()]}function Lm(t){return{extensions:[pm(),dm(t),ym(),Om(t),Rm()]}}var BT={tokenize:VT,partial:!0},hx={tokenize:XT,partial:!0},dx={tokenize:GT,partial:!0},gx={tokenize:QT,partial:!0},HT={tokenize:ZT,partial:!0},yx={name:"wwwAutolink",tokenize:jT,previous:bx},xx={name:"protocolAutolink",tokenize:YT,previous:vx},bn={name:"emailAutolink",tokenize:qT,previous:Sx},Ie={};function Bm(){return{text:Ie}}var Ll=48;for(;Ll<123;)Ie[Ll]=bn,Ll++,Ll===58?Ll=65:Ll===91&&(Ll=97);Ie[43]=bn;Ie[45]=bn;Ie[46]=bn;Ie[95]=bn;Ie[72]=[bn,xx];Ie[104]=[bn,xx];Ie[87]=[bn,yx];Ie[119]=[bn,yx];function qT(t,e,n){let l=this,i,r;return a;function a(f){return!Um(f)||!Sx.call(l,l.previous)||Hm(l.events)?n(f):(t.enter("literalAutolink"),t.enter("literalAutolinkEmail"),u(f))}function u(f){return Um(f)?(t.consume(f),u):f===64?(t.consume(f),o):n(f)}function o(f){return f===46?t.check(HT,s,c)(f):f===45||f===95||St(f)?(r=!0,t.consume(f),o):s(f)}function c(f){return t.consume(f),i=!0,o}function s(f){return r&&i&&Rt(l.previous)?(t.exit("literalAutolinkEmail"),t.exit("literalAutolink"),e(f)):n(f)}}function jT(t,e,n){let l=this;return i;function i(a){return a!==87&&a!==119||!bx.call(l,l.previous)||Hm(l.events)?n(a):(t.enter("literalAutolink"),t.enter("literalAutolinkWww"),t.check(BT,t.attempt(hx,t.attempt(dx,r),n),n)(a))}function r(a){return t.exit("literalAutolinkWww"),t.exit("literalAutolink"),e(a)}}function YT(t,e,n){let l=this,i="",r=!1;return a;function a(f){return(f===72||f===104)&&vx.call(l,l.previous)&&!Hm(l.events)?(t.enter("literalAutolink"),t.enter("literalAutolinkHttp"),i+=String.fromCodePoint(f),t.consume(f),u):n(f)}function u(f){if(Rt(f)&&i.length<5)return i+=String.fromCodePoint(f),t.consume(f),u;if(f===58){let p=i.toLowerCase();if(p==="http"||p==="https")return t.consume(f),o}return n(f)}function o(f){return f===47?(t.consume(f),r?c:(r=!0,o)):n(f)}function c(f){return f===null||zl(f)||Q(f)||Fe(f)||Cl(f)?n(f):t.attempt(hx,t.attempt(dx,s),n)(f)}function s(f){return t.exit("literalAutolinkHttp"),t.exit("literalAutolink"),e(f)}}function VT(t,e,n){let l=0;return i;function i(a){return(a===87||a===119)&&l<3?(l++,t.consume(a),i):a===46&&l===3?(t.consume(a),r):n(a)}function r(a){return a===null?n(a):e(a)}}function XT(t,e,n){let l,i,r;return a;function a(c){return c===46||c===95?t.check(gx,o,u)(c):c===null||Q(c)||Fe(c)||c!==45&&Cl(c)?o(c):(r=!0,t.consume(c),a)}function u(c){return c===95?l=!0:(i=l,l=void 0),t.consume(c),a}function o(c){return i||l||!r?n(c):e(c)}}function GT(t,e){let n=0,l=0;return i;function i(a){return a===40?(n++,t.consume(a),i):a===41&&l<n?r(a):a===33||a===34||a===38||a===39||a===41||a===42||a===44||a===46||a===58||a===59||a===60||a===63||a===93||a===95||a===126?t.check(gx,e,r)(a):a===null||Q(a)||Fe(a)?e(a):(t.consume(a),i)}function r(a){return a===41&&l++,t.consume(a),i}}function QT(t,e,n){return l;function l(u){return u===33||u===34||u===39||u===41||u===42||u===44||u===46||u===58||u===59||u===63||u===95||u===126?(t.consume(u),l):u===38?(t.consume(u),r):u===93?(t.consume(u),i):u===60||u===null||Q(u)||Fe(u)?e(u):n(u)}function i(u){return u===null||u===40||u===91||Q(u)||Fe(u)?e(u):l(u)}function r(u){return Rt(u)?a(u):n(u)}function a(u){return u===59?(t.consume(u),l):Rt(u)?(t.consume(u),a):n(u)}}function ZT(t,e,n){return l;function l(r){return t.consume(r),i}function i(r){return St(r)?n(r):e(r)}}function bx(t){return t===null||t===40||t===42||t===95||t===91||t===93||t===126||Q(t)}function vx(t){return!Rt(t)}function Sx(t){return!(t===47||Um(t))}function Um(t){return t===43||t===45||t===46||t===95||St(t)}function Hm(t){let e=t.length,n=!1;for(;e--;){let l=t[e][1];if((l.type==="labelLink"||l.type==="labelImage")&&!l._balanced){n=!0;break}if(l._gfmAutolinkLiteralWalkedInto){n=!1;break}}return t.length>0&&!n&&(t[t.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}var FT={tokenize:tA,partial:!0};function qm(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:WT,continuation:{tokenize:PT},exit:$T}},text:{91:{name:"gfmFootnoteCall",tokenize:JT},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:KT,resolveTo:IT}}}}function KT(t,e,n){let l=this,i=l.events.length,r=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),a;for(;i--;){let o=l.events[i][1];if(o.type==="labelImage"){a=o;break}if(o.type==="gfmFootnoteCall"||o.type==="labelLink"||o.type==="label"||o.type==="image"||o.type==="link")break}return u;function u(o){if(!a||!a._balanced)return n(o);let c=Jt(l.sliceSerialize({start:a.end,end:l.now()}));return c.codePointAt(0)!==94||!r.includes(c.slice(1))?n(o):(t.enter("gfmFootnoteCallLabelMarker"),t.consume(o),t.exit("gfmFootnoteCallLabelMarker"),e(o))}}function IT(t,e){let n=t.length,l;for(;n--;)if(t[n][1].type==="labelImage"&&t[n][0]==="enter"){l=t[n][1];break}t[n+1][1].type="data",t[n+3][1].type="gfmFootnoteCallLabelMarker";let i={type:"gfmFootnoteCall",start:Object.assign({},t[n+3][1].start),end:Object.assign({},t[t.length-1][1].end)},r={type:"gfmFootnoteCallMarker",start:Object.assign({},t[n+3][1].end),end:Object.assign({},t[n+3][1].end)};r.end.column++,r.end.offset++,r.end._bufferIndex++;let a={type:"gfmFootnoteCallString",start:Object.assign({},r.end),end:Object.assign({},t[t.length-1][1].start)},u={type:"chunkString",contentType:"string",start:Object.assign({},a.start),end:Object.assign({},a.end)},o=[t[n+1],t[n+2],["enter",i,e],t[n+3],t[n+4],["enter",r,e],["exit",r,e],["enter",a,e],["enter",u,e],["exit",u,e],["exit",a,e],t[t.length-2],t[t.length-1],["exit",i,e]];return t.splice(n,t.length-n+1,...o),t}function JT(t,e,n){let l=this,i=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),r=0,a;return u;function u(f){return t.enter("gfmFootnoteCall"),t.enter("gfmFootnoteCallLabelMarker"),t.consume(f),t.exit("gfmFootnoteCallLabelMarker"),o}function o(f){return f!==94?n(f):(t.enter("gfmFootnoteCallMarker"),t.consume(f),t.exit("gfmFootnoteCallMarker"),t.enter("gfmFootnoteCallString"),t.enter("chunkString").contentType="string",c)}function c(f){if(r>999||f===93&&!a||f===null||f===91||Q(f))return n(f);if(f===93){t.exit("chunkString");let p=t.exit("gfmFootnoteCallString");return i.includes(Jt(l.sliceSerialize(p)))?(t.enter("gfmFootnoteCallLabelMarker"),t.consume(f),t.exit("gfmFootnoteCallLabelMarker"),t.exit("gfmFootnoteCall"),e):n(f)}return Q(f)||(a=!0),r++,t.consume(f),f===92?s:c}function s(f){return f===91||f===92||f===93?(t.consume(f),r++,c):c(f)}}function WT(t,e,n){let l=this,i=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),r,a=0,u;return o;function o(y){return t.enter("gfmFootnoteDefinition")._container=!0,t.enter("gfmFootnoteDefinitionLabel"),t.enter("gfmFootnoteDefinitionLabelMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionLabelMarker"),c}function c(y){return y===94?(t.enter("gfmFootnoteDefinitionMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionMarker"),t.enter("gfmFootnoteDefinitionLabelString"),t.enter("chunkString").contentType="string",s):n(y)}function s(y){if(a>999||y===93&&!u||y===null||y===91||Q(y))return n(y);if(y===93){t.exit("chunkString");let v=t.exit("gfmFootnoteDefinitionLabelString");return r=Jt(l.sliceSerialize(v)),t.enter("gfmFootnoteDefinitionLabelMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionLabelMarker"),t.exit("gfmFootnoteDefinitionLabel"),p}return Q(y)||(u=!0),a++,t.consume(y),y===92?f:s}function f(y){return y===91||y===92||y===93?(t.consume(y),a++,s):s(y)}function p(y){return y===58?(t.enter("definitionMarker"),t.consume(y),t.exit("definitionMarker"),i.includes(r)||i.push(r),U(t,m,"gfmFootnoteDefinitionWhitespace")):n(y)}function m(y){return e(y)}}function PT(t,e,n){return t.check(Ke,e,t.attempt(FT,e,n))}function $T(t){t.exit("gfmFootnoteDefinition")}function tA(t,e,n){let l=this;return U(t,i,"gfmFootnoteDefinitionIndent",5);function i(r){let a=l.events[l.events.length-1];return a&&a[1].type==="gfmFootnoteDefinitionIndent"&&a[2].sliceSerialize(a[1],!0).length===4?e(r):n(r)}}function jm(t){let n=(t||{}).singleTilde,l={name:"strikethrough",tokenize:r,resolveAll:i};return n==null&&(n=!0),{text:{126:l},insideSpan:{null:[l]},attentionMarkers:{null:[126]}};function i(a,u){let o=-1;for(;++o<a.length;)if(a[o][0]==="enter"&&a[o][1].type==="strikethroughSequenceTemporary"&&a[o][1]._close){let c=o;for(;c--;)if(a[c][0]==="exit"&&a[c][1].type==="strikethroughSequenceTemporary"&&a[c][1]._open&&a[o][1].end.offset-a[o][1].start.offset===a[c][1].end.offset-a[c][1].start.offset){a[o][1].type="strikethroughSequence",a[c][1].type="strikethroughSequence";let s={type:"strikethrough",start:Object.assign({},a[c][1].start),end:Object.assign({},a[o][1].end)},f={type:"strikethroughText",start:Object.assign({},a[c][1].end),end:Object.assign({},a[o][1].start)},p=[["enter",s,u],["enter",a[c][1],u],["exit",a[c][1],u],["enter",f,u]],m=u.parser.constructs.insideSpan.null;m&&At(p,p.length,0,Wn(m,a.slice(c+1,o),u)),At(p,p.length,0,[["exit",f,u],["enter",a[o][1],u],["exit",a[o][1],u],["exit",s,u]]),At(a,c-1,o-c+3,p),o=c+p.length-2;break}}for(o=-1;++o<a.length;)a[o][1].type==="strikethroughSequenceTemporary"&&(a[o][1].type="data");return a}function r(a,u,o){let c=this.previous,s=this.events,f=0;return p;function p(y){return c===126&&s[s.length-1][1].type!=="characterEscape"?o(y):(a.enter("strikethroughSequenceTemporary"),m(y))}function m(y){let v=xn(c);if(y===126)return f>1?o(y):(a.consume(y),f++,m);if(f<2&&!n)return o(y);let T=a.exit("strikethroughSequenceTemporary"),h=xn(y);return T._open=!h||h===2&&!!v,T._close=!v||v===2&&!!h,u(y)}}}var To=class{constructor(){this.map=[]}add(e,n,l){eA(this,e,n,l)}consume(e){if(this.map.sort(function(r,a){return r[0]-a[0]}),this.map.length===0)return;let n=this.map.length,l=[];for(;n>0;)n-=1,l.push(e.slice(this.map[n][0]+this.map[n][1]),this.map[n][2]),e.length=this.map[n][0];l.push(e.slice()),e.length=0;let i=l.pop();for(;i;){for(let r of i)e.push(r);i=l.pop()}this.map.length=0}};function eA(t,e,n,l){let i=0;if(!(n===0&&l.length===0)){for(;i<t.map.length;){if(t.map[i][0]===e){t.map[i][1]+=n,t.map[i][2].push(...l);return}i+=1}t.map.push([e,n,l])}}function kx(t,e){let n=!1,l=[];for(;e<t.length;){let i=t[e];if(n){if(i[0]==="enter")i[1].type==="tableContent"&&l.push(t[e+1][1].type==="tableDelimiterMarker"?"left":"none");else if(i[1].type==="tableContent"){if(t[e-1][1].type==="tableDelimiterMarker"){let r=l.length-1;l[r]=l[r]==="left"?"center":"right"}}else if(i[1].type==="tableDelimiterRow")break}else i[0]==="enter"&&i[1].type==="tableDelimiterRow"&&(n=!0);e+=1}return l}function Ym(){return{flow:{null:{name:"table",tokenize:nA,resolveAll:lA}}}}function nA(t,e,n){let l=this,i=0,r=0,a;return u;function u(S){let J=l.events.length-1;for(;J>-1;){let q=l.events[J][1].type;if(q==="lineEnding"||q==="linePrefix")J--;else break}let Z=J>-1?l.events[J][1].type:null,N=Z==="tableHead"||Z==="tableRow"?E:o;return N===E&&l.parser.lazy[l.now().line]?n(S):N(S)}function o(S){return t.enter("tableHead"),t.enter("tableRow"),c(S)}function c(S){return S===124||(a=!0,r+=1),s(S)}function s(S){return S===null?n(S):R(S)?r>1?(r=0,l.interrupt=!0,t.exit("tableRow"),t.enter("lineEnding"),t.consume(S),t.exit("lineEnding"),m):n(S):B(S)?U(t,s,"whitespace")(S):(r+=1,a&&(a=!1,i+=1),S===124?(t.enter("tableCellDivider"),t.consume(S),t.exit("tableCellDivider"),a=!0,s):(t.enter("data"),f(S)))}function f(S){return S===null||S===124||Q(S)?(t.exit("data"),s(S)):(t.consume(S),S===92?p:f)}function p(S){return S===92||S===124?(t.consume(S),f):f(S)}function m(S){return l.interrupt=!1,l.parser.lazy[l.now().line]?n(S):(t.enter("tableDelimiterRow"),a=!1,B(S)?U(t,y,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(S):y(S))}function y(S){return S===45||S===58?T(S):S===124?(a=!0,t.enter("tableCellDivider"),t.consume(S),t.exit("tableCellDivider"),v):z(S)}function v(S){return B(S)?U(t,T,"whitespace")(S):T(S)}function T(S){return S===58?(r+=1,a=!0,t.enter("tableDelimiterMarker"),t.consume(S),t.exit("tableDelimiterMarker"),h):S===45?(r+=1,h(S)):S===null||R(S)?k(S):z(S)}function h(S){return S===45?(t.enter("tableDelimiterFiller"),d(S)):z(S)}function d(S){return S===45?(t.consume(S),d):S===58?(a=!0,t.exit("tableDelimiterFiller"),t.enter("tableDelimiterMarker"),t.consume(S),t.exit("tableDelimiterMarker"),g):(t.exit("tableDelimiterFiller"),g(S))}function g(S){return B(S)?U(t,k,"whitespace")(S):k(S)}function k(S){return S===124?y(S):S===null||R(S)?!a||i!==r?z(S):(t.exit("tableDelimiterRow"),t.exit("tableHead"),e(S)):z(S)}function z(S){return n(S)}function E(S){return t.enter("tableRow"),M(S)}function M(S){return S===124?(t.enter("tableCellDivider"),t.consume(S),t.exit("tableCellDivider"),M):S===null||R(S)?(t.exit("tableRow"),e(S)):B(S)?U(t,M,"whitespace")(S):(t.enter("data"),O(S))}function O(S){return S===null||S===124||Q(S)?(t.exit("data"),M(S)):(t.consume(S),S===92?L:O)}function L(S){return S===92||S===124?(t.consume(S),O):O(S)}}function lA(t,e){let n=-1,l=!0,i=0,r=[0,0,0,0],a=[0,0,0,0],u=!1,o=0,c,s,f,p=new To;for(;++n<t.length;){let m=t[n],y=m[1];m[0]==="enter"?y.type==="tableHead"?(u=!1,o!==0&&(Ex(p,e,o,c,s),s=void 0,o=0),c={type:"table",start:Object.assign({},y.start),end:Object.assign({},y.end)},p.add(n,0,[["enter",c,e]])):y.type==="tableRow"||y.type==="tableDelimiterRow"?(l=!0,f=void 0,r=[0,0,0,0],a=[0,n+1,0,0],u&&(u=!1,s={type:"tableBody",start:Object.assign({},y.start),end:Object.assign({},y.end)},p.add(n,0,[["enter",s,e]])),i=y.type==="tableDelimiterRow"?2:s?3:1):i&&(y.type==="data"||y.type==="tableDelimiterMarker"||y.type==="tableDelimiterFiller")?(l=!1,a[2]===0&&(r[1]!==0&&(a[0]=a[1],f=Ao(p,e,r,i,void 0,f),r=[0,0,0,0]),a[2]=n)):y.type==="tableCellDivider"&&(l?l=!1:(r[1]!==0&&(a[0]=a[1],f=Ao(p,e,r,i,void 0,f)),r=a,a=[r[1],n,0,0])):y.type==="tableHead"?(u=!0,o=n):y.type==="tableRow"||y.type==="tableDelimiterRow"?(o=n,r[1]!==0?(a[0]=a[1],f=Ao(p,e,r,i,n,f)):a[1]!==0&&(f=Ao(p,e,a,i,n,f)),i=0):i&&(y.type==="data"||y.type==="tableDelimiterMarker"||y.type==="tableDelimiterFiller")&&(a[3]=n)}for(o!==0&&Ex(p,e,o,c,s),p.consume(e.events),n=-1;++n<e.events.length;){let m=e.events[n];m[0]==="enter"&&m[1].type==="table"&&(m[1]._align=kx(e.events,n))}return t}function Ao(t,e,n,l,i,r){let a=l===1?"tableHeader":l===2?"tableDelimiter":"tableData",u="tableContent";n[0]!==0&&(r.end=Object.assign({},Yi(e.events,n[0])),t.add(n[0],0,[["exit",r,e]]));let o=Yi(e.events,n[1]);if(r={type:a,start:Object.assign({},o),end:Object.assign({},o)},t.add(n[1],0,[["enter",r,e]]),n[2]!==0){let c=Yi(e.events,n[2]),s=Yi(e.events,n[3]),f={type:u,start:Object.assign({},c),end:Object.assign({},s)};if(t.add(n[2],0,[["enter",f,e]]),l!==2){let p=e.events[n[2]],m=e.events[n[3]];if(p[1].end=Object.assign({},m[1].end),p[1].type="chunkText",p[1].contentType="text",n[3]>n[2]+1){let y=n[2]+1,v=n[3]-n[2]-1;t.add(y,v,[])}}t.add(n[3]+1,0,[["exit",f,e]])}return i!==void 0&&(r.end=Object.assign({},Yi(e.events,i)),t.add(i,0,[["exit",r,e]]),r=void 0),r}function Ex(t,e,n,l,i){let r=[],a=Yi(e.events,n);i&&(i.end=Object.assign({},a),r.push(["exit",i,e])),l.end=Object.assign({},a),r.push(["exit",l,e]),t.add(n+1,0,r)}function Yi(t,e){let n=t[e],l=n[0]==="enter"?"start":"end";return n[1][l]}var iA={name:"tasklistCheck",tokenize:rA};function Vm(){return{text:{91:iA}}}function rA(t,e,n){let l=this;return i;function i(o){return l.previous!==null||!l._gfmTasklistFirstContentOfListItem?n(o):(t.enter("taskListCheck"),t.enter("taskListCheckMarker"),t.consume(o),t.exit("taskListCheckMarker"),r)}function r(o){return Q(o)?(t.enter("taskListCheckValueUnchecked"),t.consume(o),t.exit("taskListCheckValueUnchecked"),a):o===88||o===120?(t.enter("taskListCheckValueChecked"),t.consume(o),t.exit("taskListCheckValueChecked"),a):n(o)}function a(o){return o===93?(t.enter("taskListCheckMarker"),t.consume(o),t.exit("taskListCheckMarker"),t.exit("taskListCheck"),u):n(o)}function u(o){return R(o)?e(o):B(o)?t.check({tokenize:aA},e,n)(o):n(o)}}function aA(t,e,n){return U(t,l,"whitespace");function l(i){return i===null?n(i):e(i)}}function Tx(t){return Wu([Bm(),qm(),jm(t),Ym(),Vm()])}var uA={};function wo(t){let e=this,n=t||uA,l=e.data(),i=l.micromarkExtensions||(l.micromarkExtensions=[]),r=l.fromMarkdownExtensions||(l.fromMarkdownExtensions=[]),a=l.toMarkdownExtensions||(l.toMarkdownExtensions=[]);i.push(Tx(n)),r.push(Nm()),a.push(Lm(n))}function Xm(t){return!t||typeof t!="string"?"":t.replace(/\\n/g,`
`).replace(/\\r\\n/g,`
`).replace(/\\r/g,`
`).replace(/([a-zA-Z])""/g,"$1").replace(/""([a-zA-Z])/g,"$1").replace(/""([!?.,;:])/g,"$1").replace(/([!?.,;:])""/g,"$1").replace(/""'/g,"'").replace(/'""/g,"'").replace(/"""/g,'"').replace(/""/g,"").replace(/\\"/g,'"').replace(/\\""/g,'"').replace(/"{2,}/g,'"').replace(/"([^"]*)""/g,'"$1"').replace(/""([^"]*)""/g,'"$1"').replace(/\\\\"/g,'"').replace(/"\s*"/g,'"').replace(/([a-zA-Z])"([a-zA-Z])/g,"$1'$2").replace(/\s+"/g,' "').replace(/"\s+/g,'" ').trim().replace(/^["']+(.*)["']+$/,"$1").replace(/^["']\s*([\s\S]*?)\s*["']$/,"$1").replace(/^["']+\s*/,"").replace(/\s*["']+$/,"").trim()}var P=vn(Ju()),dt={container:{position:"fixed",bottom:"20px",right:"20px",zIndex:2147483647,fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontSize:"14px",lineHeight:"1.5",color:"#333",pointerEvents:"auto"},containerLeft:{left:"20px",right:"auto"},bubble:{width:"60px",height:"60px",borderRadius:"50%",border:"none",cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",transition:"all 0.3s ease",outline:"none",position:"relative"},bubbleHover:{transform:"scale(1.05)",boxShadow:"0 6px 16px rgba(0, 0, 0, 0.2)"},chatWindow:{position:"absolute",bottom:"70px",right:"0",width:"360px",height:"600px",maxHeight:"calc(100vh - 100px)",backgroundColor:"#ffffff",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.12)",border:"1px solid #e5e7eb",display:"flex",flexDirection:"column",overflow:"hidden",transform:"translateY(10px)",opacity:0,transition:"all 0.3s ease",pointerEvents:"auto"},chatWindowLeft:{left:"0",right:"auto"},chatWindowOpen:{transform:"translateY(0)",opacity:1},header:{padding:"16px 20px",borderBottom:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"space-between",backgroundColor:"#ffffff"},headerTitle:{margin:0,fontSize:"16px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontWeight:"600",color:"#111827"},closeButton:{background:"none",border:"none",cursor:"pointer",padding:"4px",borderRadius:"4px",color:"#6b7280",fontSize:"18px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',lineHeight:1,outline:"none"},messagesContainer:{flex:1,padding:"16px",overflowY:"auto",display:"flex",flexDirection:"column",gap:"12px"},message:{maxWidth:"80%",padding:"8px 12px",borderRadius:"12px",fontSize:"14px",lineHeight:"1.4"},userMessage:{alignSelf:"flex-end",backgroundColor:"#3b82f6",color:"#ffffff"},assistantMessage:{alignSelf:"flex-start",backgroundColor:"#f3f4f6",color:"#111827"},inputContainer:{padding:"16px",borderTop:"1px solid #e5e7eb",display:"flex",gap:"8px",backgroundColor:"#ffffff",color:"#111827 !important"},input:{flex:1,padding:"8px 12px",border:"1px solid #d1d5db",borderRadius:"8px",fontSize:"14px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',outline:"none",resize:"none",minHeight:"36px",maxHeight:"100px",backgroundColor:"#ffffff"},sendButton:{padding:"8px 16px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontWeight:"500",outline:"none",transition:"all 0.2s ease"},sendButtonDisabled:{opacity:.5,cursor:"not-allowed"},brandingFooter:{padding:"8px 16px",textAlign:"center",borderTop:"1px solid #f3f4f6",backgroundColor:"#fafafa"},brandingText:{fontSize:"11px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',color:"#9ca3af",margin:0},brandingLink:{color:"#6b7280",textDecoration:"none",fontWeight:"500",transition:"color 0.2s ease"},markdownContent:{lineHeight:"1.6"}};function Ax({content:t,primaryColor:e}){return(0,P.jsx)("div",{style:dt.markdownContent,children:(0,P.jsx)(rm,{remarkPlugins:[wo],components:{p:({children:n})=>(0,P.jsx)("p",{style:{margin:"8px 0",color:"inherit"},children:n}),ul:({children:n})=>(0,P.jsx)("ul",{style:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},children:n}),ol:({children:n})=>(0,P.jsx)("ol",{style:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},children:n}),li:({children:n})=>(0,P.jsx)("li",{style:{margin:"4px 0",color:"inherit"},children:n}),code:({children:n,className:l})=>l?(0,P.jsx)("code",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit",display:"block"},children:n}):(0,P.jsx)("code",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"2px 4px",borderRadius:"3px",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit"},children:n}),a:({children:n,href:l})=>(0,P.jsx)("a",{href:l,style:{color:e,textDecoration:"underline"},target:"_blank",rel:"noopener noreferrer",children:n}),strong:({children:n})=>(0,P.jsx)("strong",{style:{fontWeight:600,color:"inherit"},children:n}),em:({children:n})=>(0,P.jsx)("em",{style:{fontStyle:"italic",color:"inherit"},children:n})},children:t})})}function oA({config:t,shadowRoot:e}){let[n,l]=(0,Zt.useState)(t.initiallyOpen||!1),[i,r]=(0,Zt.useState)([]),[a,u]=(0,Zt.useState)(""),[o,c]=(0,Zt.useState)(!1),[s,f]=(0,Zt.useState)(!1),[p,m]=(0,Zt.useState)(t.conversationId||null),y=(0,Zt.useRef)(null),v=(0,Zt.useRef)(null),T=t.primaryColor||"#3b82f6",h=t.secondaryColor||"#f3f4f6",d=t.position==="bottom-left",g=C(C({},dt.container),d?dt.containerLeft:{}),k=C(C(C({},dt.chatWindow),d?dt.chatWindowLeft:{}),n?dt.chatWindowOpen:{});(0,Zt.useEffect)(()=>{y.current&&y.current.scrollIntoView({behavior:"smooth"})}),(0,Zt.useEffect)(()=>{Hl(this,null,function*(){if(p)try{let L=t.baseUrl||"",S=yield fetch(`${L}/api/conversations/${p}/messages`,{method:"GET",headers:{"Content-Type":"application/json"}});if(S.ok){let J=yield S.json();if(J.messages&&Array.isArray(J.messages)){let Z=J.messages.map(N=>({id:N.id,content:N.content,role:N.role,timestamp:new Date(N.createdAt)}));r(Z)}}}catch(L){console.error("[Widget] Error loading conversation history:",L)}})},[p,t.baseUrl]);let z=(0,Zt.useCallback)(O=>{u(O.target.value);let L=O.target;L.style.height="auto",L.style.height=`${Math.min(L.scrollHeight,100)}px`},[]),E=(0,Zt.useCallback)(O=>{O.key==="Enter"&&!O.shiftKey&&(O.preventDefault(),a.trim()&&!o&&M())},[a,o]),M=(0,Zt.useCallback)(()=>Hl(this,null,function*(){var J,Z;if(!a.trim()||o)return;let O={id:`user-${Date.now()}`,content:a.trim(),role:"user",timestamp:new Date};r(N=>[...N,O]),u(""),c(!0),v.current&&(v.current.style.height="auto");let L=`assistant-${Date.now()}`,S={id:L,content:"Thinking...",role:"assistant",timestamp:new Date};r(N=>[...N,S]);try{let N=t.baseUrl||"",q=yield fetch(`${N}/api/chat/${t.websiteId}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:a.trim(),visitorId:t.visitorId,conversationId:p})});if(!q.ok)throw new Error(`HTTP error! status: ${q.status}`);let j=(J=q.body)==null?void 0:J.getReader();if(!j)throw new Error("No response body");let it="";for(;;){let{done:gt,value:jt}=yield j.read();if(gt)break;let x=new TextDecoder().decode(jt).split(`
`);for(let Dt of x)if(Dt.trim()!==""){if(Dt.startsWith("0:"))try{let wt=JSON.parse(Dt.slice(2));it+=wt,o&&wt.trim()&&c(!1);let b=Xm(it);r(Yt=>Yt.map(ce=>ce.id===L?Nt(C({},ce),{content:b}):ce))}catch(wt){console.warn("[Widget] Failed to parse text content:",wt);let b=Dt.slice(2);it+=b,o&&b.trim()&&c(!1);let Yt=Xm(it);r(ce=>ce.map(el=>el.id===L?Nt(C({},el),{content:Yt}):el))}else if(Dt.startsWith("8:"))try{let wt=JSON.parse(Dt.slice(2));wt.conversationId&&!p&&(m(wt.conversationId),(Z=t.onConversationIdChange)==null||Z.call(t,wt.conversationId))}catch(wt){console.warn("[Widget] Failed to parse conversation data:",wt)}}}it||r(gt=>gt.map(jt=>jt.id===L?Nt(C({},jt),{content:"Sorry, I encountered an error."}):jt))}catch(N){console.error("Error sending message:",N),r(q=>q.map(j=>j.id===L?Nt(C({},j),{content:"Sorry, I encountered an error. Please try again."}):j))}finally{c(!1)}}),[a,o,t,p]);return(0,P.jsxs)("div",{style:g,children:[n&&(0,P.jsxs)("div",{style:k,children:[(0,P.jsxs)("div",{style:dt.header,children:[(0,P.jsx)("h3",{style:dt.headerTitle,children:t.headerText||"Chat with us"}),(0,P.jsx)("button",{type:"button",style:dt.closeButton,onClick:()=>l(!1),"aria-label":"Close chat",children:"\xD7"})]}),(0,P.jsxs)("div",{style:dt.messagesContainer,children:[i.length===0&&t.welcomeMessage&&(0,P.jsx)("div",{style:C(C({},dt.message),dt.assistantMessage),children:(0,P.jsx)(Ax,{content:t.welcomeMessage,primaryColor:T})}),i.map(O=>(0,P.jsx)("div",{style:C(C({},dt.message),O.role==="user"?dt.userMessage:dt.assistantMessage),children:O.role==="assistant"?(0,P.jsx)(Ax,{content:O.content,primaryColor:T}):O.content},O.id)),(0,P.jsx)("div",{ref:y})]}),(0,P.jsxs)("div",{style:dt.inputContainer,children:[(0,P.jsx)("textarea",{ref:v,style:Nt(C({},dt.input),{color:"#111827"}),value:a,onChange:z,onKeyDown:E,placeholder:"Type your message...",disabled:o,rows:1}),(0,P.jsx)("button",{type:"button",style:C(Nt(C({},dt.sendButton),{backgroundColor:T,color:"#ffffff"}),o||!a.trim()?dt.sendButtonDisabled:{}),onClick:M,disabled:o||!a.trim(),children:"Send"})]}),(0,P.jsx)("div",{style:dt.brandingFooter,children:(0,P.jsxs)("p",{style:dt.brandingText,children:["Powered by"," ",(0,P.jsx)("a",{href:"https://bublai.com",target:"_blank",rel:"noopener noreferrer",style:dt.brandingLink,children:"Bubl"})]})})]}),(0,P.jsx)("button",{type:"button",style:C(Nt(C({},dt.bubble),{backgroundColor:T}),s?dt.bubbleHover:{}),onClick:()=>l(!n),onMouseEnter:()=>f(!0),onMouseLeave:()=>f(!1),"aria-label":n?"Close chat":"Open chat",children:n?(0,P.jsx)("span",{style:{color:"#ffffff",fontSize:"24px"},children:"\xD7"}):(0,P.jsx)("span",{style:{color:"#ffffff",fontSize:"24px"},children:"\u{1F4AC}"})})]})}function zx(t){let e=document.createElement("div");e.id="bubl-chat-widget",document.body.appendChild(e);let n=e.attachShadow({mode:"open"}),l=(0,wx.createRoot)(n);return l.render((0,P.jsx)(oA,{config:t,shadowRoot:n})),()=>{l.unmount(),e.remove()}}window.BublWidgetV2={init:zx};})();
/*! Bundled license information:

react/cjs/react.production.js:
  (**
   * @license React
   * react.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

scheduler/cjs/scheduler.production.js:
  (**
   * @license React
   * scheduler.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom.production.js:
  (**
   * @license React
   * react-dom.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom-client.production.js:
  (**
   * @license React
   * react-dom-client.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react/cjs/react-jsx-runtime.production.js:
  (**
   * @license React
   * react-jsx-runtime.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
